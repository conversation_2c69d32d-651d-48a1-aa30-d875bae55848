{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "لوحة تحكم الباركود" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-barcode me-2"></i>{% trans "لوحة تحكم الباركود" %}</h5>
                </div>
                <div class="card-body">
                    <!-- إحصائيات الباركود -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3>{{ total_barcodes }}</h3>
                                    <p class="mb-0">{% trans "إجمالي الباركودات" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3>{{ barcode_types_count }}</h3>
                                    <p class="mb-0">{% trans "أنواع الباركود" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3>{{ barcodes_printed }}</h3>
                                    <p class="mb-0">{% trans "الباركودات المطبوعة" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h3>{{ barcodes_scanned }}</h3>
                                    <p class="mb-0">{% trans "الباركودات الممسوحة" %}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- وظائف الباركود الرئيسية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="border-bottom pb-2">{% trans "وظائف الباركود" %}</h5>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-cog me-2"></i>{% trans "إدارة الباركود" %}</h5>
                                    <p class="card-text">{% trans "إدارة أنواع الباركود وإعدادات النظام" %}</p>
                                    <div class="d-grid gap-2">
                                        <a href="{% url 'inventory:barcode:barcode_types' %}" class="btn btn-outline-primary"><i class="fas fa-tags me-2"></i>{% trans "أنواع الباركود" %}</a>
                                        <a href="{% url 'inventory:barcode:barcode_settings' %}" class="btn btn-outline-secondary"><i class="fas fa-sliders-h me-2"></i>{% trans "إعدادات الباركود" %}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-print me-2"></i>{% trans "إنشاء وطباعة الباركود" %}</h5>
                                    <p class="card-text">{% trans "إنشاء باركود جديد أو طباعة باركود لمنتجات متعددة" %}</p>
                                    <div class="d-grid gap-2">
                                        <a href="{% url 'inventory:barcode:generate_barcode' %}" class="btn btn-outline-success"><i class="fas fa-plus-circle me-2"></i>{% trans "إنشاء باركود" %}</a>
                                        <a href="{% url 'inventory:select_products_for_barcode' %}" class="btn btn-outline-info"><i class="fas fa-print me-2"></i>{% trans "طباعة دفعة باركود" %}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-qrcode me-2"></i>{% trans "مسح وتتبع الباركود" %}</h5>
                                    <p class="card-text">{% trans "مسح الباركود باستخدام الكاميرا وعرض سجلات الاستخدام" %}</p>
                                    <div class="d-grid gap-2">
                                        <a href="{% url 'inventory:barcode:scan_barcode' %}" class="btn btn-outline-danger"><i class="fas fa-camera me-2"></i>{% trans "مسح الباركود" %}</a>
                                        <a href="{% url 'inventory:barcode:barcode_logs' %}" class="btn btn-outline-dark"><i class="fas fa-history me-2"></i>{% trans "سجلات الباركود" %}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ميزات متقدمة -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="border-bottom pb-2">{% trans "ميزات متقدمة" %}</h5>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-file-export me-2"></i>{% trans "تصدير واستيراد" %}</h5>
                                    <p class="card-text">{% trans "تصدير قائمة الباركود إلى ملفات Excel/CSV أو استيراد الباركود من ملفات خارجية" %}</p>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-outline-primary" onclick="alert('سيتم تطوير هذه الميزة قريباً')"><i class="fas fa-file-export me-2"></i>{% trans "تصدير الباركود" %}</button>
                                        <button class="btn btn-outline-secondary" onclick="alert('سيتم تطوير هذه الميزة قريباً')"><i class="fas fa-file-import me-2"></i>{% trans "استيراد الباركود" %}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-cogs me-2"></i>{% trans "تكامل مع نظام نقطة البيع" %}</h5>
                                    <p class="card-text">{% trans "ربط مباشر بين مسح الباركود وإضافة المنتجات إلى سلة المبيعات" %}</p>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-outline-success" onclick="alert('سيتم تطوير هذه الميزة قريباً')"><i class="fas fa-link me-2"></i>{% trans "ربط مع نقطة البيع" %}</button>
                                        <button class="btn btn-outline-info" onclick="alert('سيتم تطوير هذه الميزة قريباً')"><i class="fas fa-sync me-2"></i>{% trans "تحديث المخزون تلقائياً" %}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الأنشطة الأخيرة -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>{% trans "الأنشطة الأخيرة" %}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>{% trans "التاريخ" %}</th>
                                                    <th>{% trans "الباركود" %}</th>
                                                    <th>{% trans "العملية" %}</th>
                                                    <th>{% trans "المستخدم" %}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for log in recent_logs %}
                                                <tr>
                                                    <td>{{ log.created_at|date:"Y-m-d H:i" }}</td>
                                                    <td>{{ log.barcode.barcode_number }}</td>
                                                    <td>
                                                        {% if log.action == 'print' %}
                                                            <span class="badge bg-warning">{% trans "طباعة" %}</span>
                                                        {% elif log.action == 'scan' %}
                                                            <span class="badge bg-success">{% trans "مسح" %}</span>
                                                        {% elif log.action == 'create' %}
                                                            <span class="badge bg-primary">{% trans "إنشاء" %}</span>
                                                        {% elif log.action == 'delete' %}
                                                            <span class="badge bg-danger">{% trans "حذف" %}</span>
                                                        {% else %}
                                                            <span class="badge bg-secondary">{{ log.action }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ log.user.username }}</td>
                                                </tr>
                                                {% empty %}
                                                <tr>
                                                    <td colspan="4" class="text-center">{% trans "لا توجد أنشطة حديثة" %}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // يمكن إضافة أي سلوك JavaScript إضافي هنا
    });
</script>
{% endblock %}