{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "اختيار المنتجات لطباعة الباركود" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-print me-2"></i>{% trans "اختيار المنتجات لطباعة الباركود" %}</h5>
                </div>
                <div class="card-body">
                    <form action="{% url 'inventory:bulk_print_barcodes' %}" method="post">
                        {% csrf_token %}
                        
                        <!-- عدد النسخ -->
                        <div class="mb-3">
                            <label for="copies" class="form-label">{% trans "عدد النسخ لكل باركود" %}</label>
                            <input type="number" class="form-control" id="copies" name="copies" min="1" max="100" value="1">
                            <div class="form-text">{% trans "أقصى عدد نسخ مسموح به هو 100 نسخة" %}</div>
                        </div>
                        
                        <!-- البحث عن المنتجات -->
                        <div class="mb-3">
                            <label for="product_search" class="form-label">{% trans "البحث عن منتج" %}</label>
                            <input type="text" class="form-control" id="product_search" placeholder="{% trans 'اكتب اسم المنتج أو الكود للبحث' %}">
                        </div>
                        
                        <!-- قائمة المنتجات -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="products_table">
                                <thead>
                                    <tr>
                                        <th width="50px">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="select_all">
                                            </div>
                                        </th>
                                        <th>{% trans "الكود" %}</th>
                                        <th>{% trans "اسم المنتج" %}</th>
                                        <th>{% trans "الفئة" %}</th>
                                        <th>{% trans "الكمية" %}</th>
                                        <th>{% trans "السعر" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for product in products %}
                                    <tr>
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input product-checkbox" type="checkbox" name="product_ids" value="{{ product.id }}">
                                            </div>
                                        </td>
                                        <td>{{ product.code }}</td>
                                        <td>{{ product.name }}</td>
                                        <td>{{ product.category.name }}</td>
                                        <td>{{ product.quantity }}</td>
                                        <td>{{ product.selling_price }}</td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="6" class="text-center">{% trans "لا توجد منتجات متاحة" %}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- أزرار التحكم -->
                        <div class="mt-3 d-flex justify-content-between">
                            <a href="{% url 'inventory:barcode_dashboard' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "العودة إلى لوحة التحكم" %}
                            </a>
                            <button type="submit" class="btn btn-primary" id="print_button" disabled>
                                <i class="fas fa-print me-2"></i>{% trans "طباعة الباركودات" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // تفعيل/تعطيل زر الطباعة بناءً على اختيار المنتجات
        function updatePrintButton() {
            var checkedProducts = $('.product-checkbox:checked').length;
            $('#print_button').prop('disabled', checkedProducts === 0);
        }
        
        // تحديث حالة زر الطباعة عند تغيير حالة أي خانة اختيار
        $('.product-checkbox').on('change', updatePrintButton);
        
        // تحديد/إلغاء تحديد كل المنتجات
        $('#select_all').on('change', function() {
            $('.product-checkbox').prop('checked', $(this).prop('checked'));
            updatePrintButton();
        });
        
        // البحث في المنتجات
        $('#product_search').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            $("#products_table tbody tr").filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });
    });
</script>
{% endblock %}