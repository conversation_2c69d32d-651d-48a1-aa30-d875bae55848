{% extends 'base.html' %}
{% load i18n static humanize %}

{% block title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:index' %}">{% trans "الرئيسية" %}</a></li>
<li class="breadcrumb-item"><a href="{% url 'inventory:barcode_settings' %}">{% trans "إعدادات الباركود" %}</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h3 class="card-title">{{ title }}</h3>
    </div>
    <div class="card-body">
        <form method="get" class="mb-3">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="product">{% trans "المنتج" %}</label>
                        <input type="text" name="product" id="product" class="form-control" value="{{ request.GET.product }}">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="action">{% trans "الإجراء" %}</label>
                        <select name="action" id="action" class="form-control">
                            <option value="">{% trans "الكل" %}</option>
                            <option value="create" {% if request.GET.action == 'create' %}selected{% endif %}>{% trans "إنشاء" %}</option>
                            <option value="update" {% if request.GET.action == 'update' %}selected{% endif %}>{% trans "تحديث" %}</option>
                            <option value="delete" {% if request.GET.action == 'delete' %}selected{% endif %}>{% trans "حذف" %}</option>
                            <option value="print" {% if request.GET.action == 'print' %}selected{% endif %}>{% trans "طباعة" %}</option>
                            <option value="scan" {% if request.GET.action == 'scan' %}selected{% endif %}>{% trans "مسح" %}</option>
                            <option value="product_lookup" {% if request.GET.action == 'product_lookup' %}selected{% endif %}>{% trans "بحث عن منتج" %}</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="date_from">{% trans "من تاريخ" %}</label>
                        <input type="date" name="date_from" id="date_from" class="form-control" value="{{ request.GET.date_from }}">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="date_to">{% trans "إلى تاريخ" %}</label>
                        <input type="date" name="date_to" id="date_to" class="form-control" value="{{ request.GET.date_to }}">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="user">{% trans "المستخدم" %}</label>
                        <input type="text" name="user" id="user" class="form-control" value="{{ request.GET.user }}">
                    </div>
                </div>
                <div class="col-md-1 align-self-end">
                    <button type="submit" class="btn btn-primary btn-block">{% trans "بحث" %}</button>
                </div>
            </div>
        </form>

        {% if page_obj %}
        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>{% trans "#" %}</th>
                        <th>{% trans "المنتج" %}</th>
                        <th>{% trans "رقم الباركود" %}</th>
                        <th>{% trans "الإجراء" %}</th>
                        <th>{% trans "التفاصيل" %}</th>
                        <th>{% trans "المستخدم" %}</th>
                        <th>{% trans "تاريخ الإنشاء" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in page_obj %}
                    <tr>
                        <td>{{ forloop.counter0|add:page_obj.start_index }}</td>
                        <td>{{ log.product.name|default:"N/A" }}</td>
                        <td>{% if log.barcode_number %}{{ log.barcode_number }}{% elif log.barcode %}{{ log.barcode.barcode_number }}{% else %}N/A{% endif %}</td>
                        <td>{{ log.get_action_display }}</td>
                        <td>{{ log.details }}</td>
                        <td>{% if log.user %}{{ log.user.username }}{% elif log.scanned_by %}{{ log.scanned_by.username }}{% else %}N/A{% endif %}</td>
                        <td>{{ log.created_at|naturaltime }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center">{% trans "لا توجد سجلات لعرضها." %}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% include 'includes/pagination.html' %}
        {% else %}
        <p class="text-center">{% trans "لا توجد سجلات لعرضها." %}</p>
        {% endif %}
    </div>
</div>
{% endblock %}