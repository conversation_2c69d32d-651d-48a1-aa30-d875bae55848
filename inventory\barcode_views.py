from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse
from django.utils.translation import gettext_lazy as _
from django.shortcuts import redirect
from django.db import transaction
from django.core.paginator import Paginator
from django.views.decorators.http import require_POST
from django.conf import settings

from .models import Product
from .barcode_models import BarcodeType, Barcode, BarcodeLog, BarcodeSettings

import barcode
from barcode.writer import ImageWriter
from io import BytesIO
import base64
import json
import uuid

@login_required
def barcode_settings(request):
    """عرض وتحديث إعدادات الباركود"""
    settings = BarcodeSettings.get_settings()
    barcode_types = BarcodeType.objects.filter(is_active=True)

    if request.method == 'POST':
        # تحديث الإعدادات
        default_barcode_type_id = request.POST.get('default_barcode_type')
        default_include_price = request.POST.get('default_include_price') == 'on'
        default_include_name = request.POST.get('default_include_name') == 'on'

        # أبعاد الملصق
        label_width = int(request.POST.get('label_width', 50))
        label_height = int(request.POST.get('label_height', 30))
        labels_per_row = int(request.POST.get('labels_per_row', 3))
        labels_per_column = int(request.POST.get('labels_per_column', 8))

        # الهوامش
        margin_top = int(request.POST.get('margin_top', 10))
        margin_right = int(request.POST.get('margin_right', 10))
        margin_bottom = int(request.POST.get('margin_bottom', 10))
        margin_left = int(request.POST.get('margin_left', 10))

        # تحديث الإعدادات
        settings.default_barcode_type_id = default_barcode_type_id
        settings.default_include_price = default_include_price
        settings.default_include_name = default_include_name
        settings.label_width = label_width
        settings.label_height = label_height
        settings.labels_per_row = labels_per_row
        settings.labels_per_column = labels_per_column
        settings.margin_top = margin_top
        settings.margin_right = margin_right
        settings.margin_bottom = margin_bottom
        settings.margin_left = margin_left
        settings.updated_by = request.user
        settings.save()

        messages.success(request, _('تم تحديث إعدادات الباركود بنجاح'))
        return redirect('inventory:barcode_settings')

    context = {
        'settings': settings,
        'barcode_types': barcode_types,
        'title': _('إعدادات الباركود'),
    }
    return render(request, 'inventory/barcode_settings.html', context)

@login_required
def barcode_types(request):
    """عرض وإدارة أنواع الباركود"""
    barcode_types = BarcodeType.objects.all()

    context = {
        'barcode_types': barcode_types,
        'title': _('أنواع الباركود'),
    }
    return render(request, 'inventory/barcode_types.html', context)

@login_required
def add_barcode_type(request):
    """إضافة نوع باركود جديد"""
    if request.method == 'POST':
        name = request.POST.get('name')
        code = request.POST.get('code')
        description = request.POST.get('description')

        if not name or not code:
            messages.error(request, _('يرجى تعبئة جميع الحقول المطلوبة'))
            return redirect('inventory:add_barcode_type')

        # التحقق من عدم وجود نوع باركود بنفس الرمز
        if BarcodeType.objects.filter(code=code).exists():
            messages.error(request, _('يوجد بالفعل نوع باركود بنفس الرمز'))
            return redirect('inventory:add_barcode_type')

        # إنشاء نوع الباركود
        barcode_type = BarcodeType.objects.create(
            name=name,
            code=code,
            description=description
        )

        messages.success(request, _('تم إضافة نوع الباركود بنجاح'))
        return redirect('inventory:barcode_types')

    context = {
        'title': _('إضافة نوع باركود'),
    }
    return render(request, 'inventory/add_barcode_type.html', context)

@login_required
def edit_barcode_type(request, type_id):
    """تعديل نوع باركود"""
    barcode_type = get_object_or_404(BarcodeType, id=type_id)

    if request.method == 'POST':
        name = request.POST.get('name')
        code = request.POST.get('code')
        description = request.POST.get('description')
        is_active = request.POST.get('is_active') == 'on'

        if not name or not code:
            messages.error(request, _('يرجى تعبئة جميع الحقول المطلوبة'))
            return redirect('inventory:edit_barcode_type', type_id=type_id)

        # التحقق من عدم وجود نوع باركود آخر بنفس الرمز
        if BarcodeType.objects.filter(code=code).exclude(id=type_id).exists():
            messages.error(request, _('يوجد بالفعل نوع باركود آخر بنفس الرمز'))
            return redirect('inventory:edit_barcode_type', type_id=type_id)

        # تحديث نوع الباركود
        barcode_type.name = name
        barcode_type.code = code
        barcode_type.description = description
        barcode_type.is_active = is_active
        barcode_type.save()

        messages.success(request, _('تم تحديث نوع الباركود بنجاح'))
        return redirect('inventory:barcode_types')

    context = {
        'barcode_type': barcode_type,
        'title': _('تعديل نوع باركود'),
    }
    return render(request, 'inventory/edit_barcode_type.html', context)

@login_required
def delete_barcode_type(request, type_id):
    """حذف نوع باركود"""
    barcode_type = get_object_or_404(BarcodeType, id=type_id)

    # التحقق من عدم وجود باركودات مرتبطة بهذا النوع
    if Barcode.objects.filter(barcode_type=barcode_type).exists():
        messages.error(request, _('لا يمكن حذف نوع الباركود لأنه مرتبط بباركودات'))
        return redirect('inventory:barcode_types')

    barcode_type.delete()
    messages.success(request, _('تم حذف نوع الباركود بنجاح'))
    return redirect('inventory:barcode_types')

@login_required
def product_barcodes(request, product_id):
    """عرض وإدارة باركودات المنتج"""
    product = get_object_or_404(Product, id=product_id)
    barcodes = Barcode.objects.filter(product=product)
    barcode_types = BarcodeType.objects.filter(is_active=True)
    settings = BarcodeSettings.get_settings()

    context = {
        'product': product,
        'barcodes': barcodes,
        'barcode_types': barcode_types,
        'settings': settings,
        'title': _('باركودات المنتج: {}').format(product.name),
    }
    return render(request, 'inventory/product_barcodes.html', context)

@login_required
def add_product_barcode(request, product_id):
    """إضافة باركود جديد للمنتج"""
    product = get_object_or_404(Product, id=product_id)
    settings = BarcodeSettings.get_settings()

    if request.method == 'POST':
        barcode_type_id = request.POST.get('barcode_type')
        barcode_number = request.POST.get('barcode_number')
        is_primary = request.POST.get('is_primary') == 'on'
        include_price = request.POST.get('include_price') == 'on'
        include_name = request.POST.get('include_name') == 'on'

        if not barcode_type_id or not barcode_number:
            messages.error(request, _('يرجى تعبئة جميع الحقول المطلوبة'))
            return redirect('inventory:add_product_barcode', product_id=product_id)

        barcode_type = get_object_or_404(BarcodeType, id=barcode_type_id)

        # التحقق من عدم وجود باركود بنفس الرقم
        if Barcode.objects.filter(barcode_number=barcode_number).exists():
            messages.error(request, _('يوجد بالفعل باركود بنفس الرقم'))
            return redirect('inventory:add_product_barcode', product_id=product_id)

        # إنشاء الباركود
        with transaction.atomic():
            barcode = Barcode.objects.create(
                product=product,
                barcode_type=barcode_type,
                barcode_number=barcode_number,
                is_primary=is_primary,
                include_price=include_price,
                include_name=include_name,
                created_by=request.user
            )

            # تسجيل العملية
            BarcodeLog.objects.create(
                barcode=barcode,
                product=product,
                action='create',
                barcode_number=barcode_number,
                user=request.user,
                details=_('تم إنشاء باركود جديد')
            )

        messages.success(request, _('تم إضافة الباركود بنجاح'))
        return redirect('inventory:product_barcodes', product_id=product_id)

    # الحصول على نوع الباركود الافتراضي والإعدادات
    barcode_types = BarcodeType.objects.filter(is_active=True)

    context = {
        'product': product,
        'barcode_types': barcode_types,
        'settings': settings,
        'title': _('إضافة باركود للمنتج: {}').format(product.name),
    }
    return render(request, 'inventory/add_product_barcode.html', context)

@login_required
def edit_product_barcode(request, barcode_id):
    """تعديل باركود المنتج"""
    barcode_obj = get_object_or_404(Barcode, id=barcode_id)
    product = barcode_obj.product

    if request.method == 'POST':
        barcode_type_id = request.POST.get('barcode_type')
        barcode_number = request.POST.get('barcode_number')
        is_primary = request.POST.get('is_primary') == 'on'
        is_active = request.POST.get('is_active') == 'on'
        include_price = request.POST.get('include_price') == 'on'
        include_name = request.POST.get('include_name') == 'on'

        if not barcode_type_id or not barcode_number:
            messages.error(request, _('يرجى تعبئة جميع الحقول المطلوبة'))
            return redirect('inventory:edit_product_barcode', barcode_id=barcode_id)

        barcode_type = get_object_or_404(BarcodeType, id=barcode_type_id)

        # التحقق من عدم وجود باركود آخر بنفس الرقم
        if Barcode.objects.filter(barcode_number=barcode_number).exclude(id=barcode_id).exists():
            messages.error(request, _('يوجد بالفعل باركود آخر بنفس الرقم'))
            return redirect('inventory:edit_product_barcode', barcode_id=barcode_id)

        # تحديث الباركود
        with transaction.atomic():
            old_barcode_number = barcode_obj.barcode_number
            barcode_obj.barcode_type = barcode_type
            barcode_obj.barcode_number = barcode_number
            barcode_obj.is_primary = is_primary
            barcode_obj.is_active = is_active
            barcode_obj.include_price = include_price
            barcode_obj.include_name = include_name
            barcode_obj.save()

            # تسجيل العملية
            BarcodeLog.objects.create(
                barcode=barcode_obj,
                product=product,
                action='update',
                barcode_number=barcode_number,
                user=request.user,
                details=_('تم تحديث الباركود من {} إلى {}').format(old_barcode_number, barcode_number)
            )

        messages.success(request, _('تم تحديث الباركود بنجاح'))
        return redirect('inventory:product_barcodes', product_id=product.id)

    # الحصول على أنواع الباركود
    barcode_types = BarcodeType.objects.filter(is_active=True)

    context = {
        'barcode': barcode_obj,
        'product': product,
        'barcode_types': barcode_types,
        'title': _('تعديل باركود المنتج: {}').format(product.name),
    }
    return render(request, 'inventory/edit_product_barcode.html', context)

@login_required
def delete_product_barcode(request, barcode_id):
    """حذف باركود المنتج"""
    barcode_obj = get_object_or_404(Barcode, id=barcode_id)
    product = barcode_obj.product

    with transaction.atomic():
        barcode_number = barcode_obj.barcode_number

        # تسجيل العملية قبل الحذف
        BarcodeLog.objects.create(
            barcode=None,  # لن يكون هناك باركود بعد الحذف
            product=product,
            action='delete',
            barcode_number=barcode_number,
            user=request.user,
            details=_('تم حذف الباركود')
        )

        # حذف الباركود
        barcode_obj.delete()

    messages.success(request, _('تم حذف الباركود بنجاح'))
    return redirect('inventory:product_barcodes', product_id=product.id)

@login_required
def print_product_barcode(request, barcode_id):
    """طباعة باركود المنتج"""
    barcode_obj = get_object_or_404(Barcode, id=barcode_id)
    product = barcode_obj.product
    settings = BarcodeSettings.get_settings()

    if request.method == 'POST':
        copies_str = request.POST.get('copies')
        log_copies = 1 # Default for logging and session update if POST data is bad
        if copies_str and copies_str.isdigit():
            log_copies = max(1, min(100, int(copies_str)))
        else:
            # If POST data for copies is invalid, use current session value or default for the session update
            log_copies = int(request.session.get(f'barcode_{barcode_id}_copies', 1))
        request.session[f'barcode_{barcode_id}_copies'] = log_copies

        # Log the print action initiated by POST
        BarcodeLog.objects.create(
            barcode=barcode_obj,
            product=product,
            action='print',
            barcode_number=barcode_obj.barcode_number,
            user=request.user,
            details=_('طلب طباعة {} نسخة من الباركود').format(log_copies)
        )
        # Redirect to the same page using GET to avoid form resubmission issues
        return redirect('inventory:print_product_barcode', barcode_id=barcode_obj.id)

    # For GET requests (including after redirect from POST or direct GET)
    copies = int(request.session.get(f'barcode_{barcode_id}_copies', 1))

    # إنشاء الباركود
    try:
        barcode_class = getattr(barcode, barcode_obj.barcode_type.code)
        # writer options for better image quality, consistent with other views
        options = {
            'module_height': 15.0,
            'font_size': 10,
            'text_distance': 5.0,
            'quiet_zone': 2.0
        }
        barcode_instance = barcode_class(barcode_obj.barcode_number, writer=ImageWriter())

        # إنشاء صورة الباركود
        buffer = BytesIO()
        barcode_instance.write(buffer, options=options) # Pass options to write method
        barcode_image = base64.b64encode(buffer.getvalue()).decode('utf-8')

    except AttributeError:
        messages.error(request, _('نوع الباركود غير صالح أو غير مدعوم من المكتبة: {}').format(barcode_obj.barcode_type.code))
        return redirect('inventory:product_barcodes', product_id=product.id)
    except Exception as e:
        messages.error(request, _('حدث خطأ أثناء إنشاء الباركود: {}').format(str(e)))
        return redirect('inventory:product_barcodes', product_id=product.id)

    context = {
        'barcode': barcode_obj,
        'product': product,
        'barcode_image': barcode_image,
        'copies': copies,
        'settings': settings,
        'title': _('طباعة باركود المنتج: {}').format(product.name),
    }
    return render(request, 'inventory/print_product_barcode.html', context)

@login_required
def generate_barcode(request):
    """إنشاء باركود جديد بناءً على نوع الباركود أو عرض مثال"""
    if request.method == 'POST':
        barcode_type_id = request.POST.get('barcode_type')

        if not barcode_type_id:
            return JsonResponse({'error': _('يرجى اختيار نوع الباركود')}, status=400)

        barcode_type_obj = get_object_or_404(BarcodeType, id=barcode_type_id)

        # إنشاء رقم باركود فريد بناءً على نوع الباركود
        if barcode_type_obj.code.lower() in ['ean13', 'ean8', 'upca']:
            # لأنواع EAN و UPC نحتاج أرقام فقط
            if barcode_type_obj.code.lower() == 'ean13':
                unique_id = str(uuid.uuid4().int)[:12]  # 12 رقم لـ EAN-13
            elif barcode_type_obj.code.lower() == 'ean8':
                unique_id = str(uuid.uuid4().int)[:7]   # 7 أرقام لـ EAN-8
            else:  # upca
                unique_id = str(uuid.uuid4().int)[:11]  # 11 رقم لـ UPC-A
            barcode_number = unique_id
        else:
            # لأنواع أخرى مثل Code128, Code39 يمكن استخدام أحرف وأرقام
            unique_id = str(uuid.uuid4().int)[:12]
            barcode_number = unique_id

        # التأكد من أن الباركود فريد
        while Barcode.objects.filter(barcode_number=barcode_number).exists():
            if barcode_type_obj.code.lower() in ['ean13', 'ean8', 'upca']:
                if barcode_type_obj.code.lower() == 'ean13':
                    unique_id = str(uuid.uuid4().int)[:12]
                elif barcode_type_obj.code.lower() == 'ean8':
                    unique_id = str(uuid.uuid4().int)[:7]
                else:  # upca
                    unique_id = str(uuid.uuid4().int)[:11]
                barcode_number = unique_id
            else:
                unique_id = str(uuid.uuid4().int)[:12]
                barcode_number = unique_id

        return JsonResponse({'barcode_number': barcode_number})

    elif request.method == 'GET' and (request.GET.get('type') or request.GET.get('data')):
        # عرض مثال على باركود إذا كان الطلب GET مع معلمات محددة
        barcode_type_code = request.GET.get('type', 'code128').lower() # افتراضي إلى code128
        barcode_data = request.GET.get('data', '123456789012') # بيانات افتراضية

        try:
            # التأكد من أن نوع الباركود مدعوم
            if not hasattr(barcode, barcode_type_code):
                return HttpResponse(_("نوع الباركود '{}' غير مدعوم.").format(barcode_type_code), status=400)

            barcode_class = getattr(barcode, barcode_type_code)
            # writer options for better image quality
            options = {
                'module_height': 15.0,
                'font_size': 10,
                'text_distance': 5.0,
                'quiet_zone': 2.0
            }
            barcode_instance = barcode_class(barcode_data, writer=ImageWriter())

            # إنشاء صورة الباركود
            buffer = BytesIO()
            barcode_instance.write(buffer, options=options)
            buffer.seek(0)
            return HttpResponse(buffer, content_type='image/png')

        except Exception as e:
            # يمكنك تسجيل الخطأ هنا إذا أردت
            # logger.error(f"Error generating barcode image: {e}")
            return HttpResponse(_("حدث خطأ أثناء إنشاء صورة الباركود: {}").format(str(e)), status=500)

    else:
        # عرض صفحة إنشاء الباركود
        barcode_types = BarcodeType.objects.filter(is_active=True)

        context = {
            'barcode_types': barcode_types,
            'title': _('إنشاء باركود'),
        }
        return render(request, 'inventory/barcode_generate.html', context)

    return JsonResponse({'error': _('طريقة غير مدعومة')}, status=405)

def test_barcode(request):
    """view محسن لإنشاء وعرض الباركود"""
    import barcode
    from barcode.writer import ImageWriter
    from io import BytesIO

    if request.method == 'POST':
        # إنشاء رقم باركود جديد
        barcode_type_id = request.POST.get('barcode_type')

        if not barcode_type_id:
            return JsonResponse({'error': 'يرجى اختيار نوع الباركود'}, status=400)

        try:
            barcode_type_obj = get_object_or_404(BarcodeType, id=barcode_type_id)

            # إنشاء رقم باركود فريد
            if barcode_type_obj.code.lower() in ['ean13', 'ean8', 'upca']:
                if barcode_type_obj.code.lower() == 'ean13':
                    unique_id = str(uuid.uuid4().int)[:12]
                elif barcode_type_obj.code.lower() == 'ean8':
                    unique_id = str(uuid.uuid4().int)[:7]
                else:  # upca
                    unique_id = str(uuid.uuid4().int)[:11]
                barcode_number = unique_id
            else:
                unique_id = str(uuid.uuid4().int)[:12]
                barcode_number = unique_id

            # التأكد من أن الباركود فريد
            while Barcode.objects.filter(barcode_number=barcode_number).exists():
                if barcode_type_obj.code.lower() in ['ean13', 'ean8', 'upca']:
                    if barcode_type_obj.code.lower() == 'ean13':
                        unique_id = str(uuid.uuid4().int)[:12]
                    elif barcode_type_obj.code.lower() == 'ean8':
                        unique_id = str(uuid.uuid4().int)[:7]
                    else:  # upca
                        unique_id = str(uuid.uuid4().int)[:11]
                    barcode_number = unique_id
                else:
                    unique_id = str(uuid.uuid4().int)[:12]
                    barcode_number = unique_id

            return JsonResponse({'barcode_number': barcode_number})

        except Exception as e:
            return JsonResponse({'error': f'خطأ في إنشاء الباركود: {str(e)}'}, status=500)

    elif request.method == 'GET' and (request.GET.get('type') or request.GET.get('data')):
        # عرض صورة الباركود
        barcode_type_code = request.GET.get('type', 'code128').lower()
        barcode_data = request.GET.get('data', '123456789')

        try:
            # التأكد من أن نوع الباركود مدعوم
            try:
                barcode_class = barcode.get_barcode_class(barcode_type_code)
            except Exception as e:
                return HttpResponse(f"Barcode type '{barcode_type_code}' is not supported: {str(e)}", status=400)

            # تنظيف بيانات الباركود حسب النوع
            if barcode_type_code in ['ean13', 'ean8', 'upca']:
                # إزالة أي أحرف غير رقمية
                barcode_data = ''.join(filter(str.isdigit, barcode_data))

                # التحقق من طول البيانات
                if barcode_type_code == 'ean13' and len(barcode_data) < 12:
                    barcode_data = barcode_data.zfill(12)
                elif barcode_type_code == 'ean8' and len(barcode_data) < 7:
                    barcode_data = barcode_data.zfill(7)
                elif barcode_type_code == 'upca' and len(barcode_data) < 11:
                    barcode_data = barcode_data.zfill(11)

                # قطع البيانات إذا كانت أطول من اللازم
                if barcode_type_code == 'ean13':
                    barcode_data = barcode_data[:12]
                elif barcode_type_code == 'ean8':
                    barcode_data = barcode_data[:7]
                elif barcode_type_code == 'upca':
                    barcode_data = barcode_data[:11]

            # إعدادات جودة الصورة
            options = {
                'module_height': 15.0,
                'font_size': 10,
                'text_distance': 5.0,
                'quiet_zone': 2.0
            }

            barcode_instance = barcode_class(barcode_data, writer=ImageWriter())

            # إنشاء صورة الباركود
            buffer = BytesIO()
            barcode_instance.write(buffer, options=options)
            buffer.seek(0)
            return HttpResponse(buffer, content_type='image/png')

        except Exception as e:
            error_msg = f"Error generating barcode: {str(e)}"
            print(f"Barcode generation error: {error_msg}")  # للتشخيص
            print(f"Barcode type: {barcode_type_code}, Data: {barcode_data}")  # للتشخيص
            return HttpResponse(error_msg, status=500)

    else:
        # عرض صفحة إنشاء الباركود
        barcode_types = BarcodeType.objects.filter(is_active=True)

        context = {
            'barcode_types': barcode_types,
            'title': 'إنشاء باركود',
        }
        return render(request, 'inventory/barcode_generate.html', context)

def simple_barcode_view(request):
    """عرض بسيط للباركود فقط"""
    import barcode
    from barcode.writer import ImageWriter
    from io import BytesIO

    barcode_type_code = request.GET.get('type', 'code128').lower()
    barcode_data = request.GET.get('data', '123456789')

    try:
        # إنشاء الباركود
        barcode_class = barcode.get_barcode_class(barcode_type_code)
        barcode_instance = barcode_class(barcode_data, writer=ImageWriter())

        # إعدادات الصورة
        options = {
            'module_height': 15.0,
            'font_size': 10,
            'text_distance': 5.0,
            'quiet_zone': 2.0
        }

        buffer = BytesIO()
        barcode_instance.write(buffer, options=options)
        buffer.seek(0)

        return HttpResponse(buffer, content_type='image/png')

    except Exception as e:
        return HttpResponse(f"Error: {str(e)}", status=500)

@login_required
def barcode_logs(request):
    """عرض سجلات الباركود"""
    logs = BarcodeLog.objects.all().order_by('-created_at')

    # تصفية السجلات
    product_id_str = request.GET.get('product')
    action = request.GET.get('action')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    user_id = request.GET.get('user')

    if product_id_str and product_id_str.isdigit():
        logs = logs.filter(product_id=int(product_id_str))
    elif product_id_str: # إذا لم يكن رقمًا، يمكنك تسجيل تحذير أو تجاهله
        # يمكنك إضافة رسالة تحذير هنا إذا أردت
        pass # تجاهل الفلتر إذا لم يكن رقمًا صالحًا

    if action:
        logs = logs.filter(action=action)

    if date_from:
        logs = logs.filter(created_at__gte=date_from)

    if date_to:
        logs = logs.filter(created_at__lte=date_to)

    if user_id and user_id.isdigit():
        logs = logs.filter(user_id=int(user_id))
    elif user_id: # إذا لم يكن رقمًا، يمكنك تسجيل تحذير أو تجاهله
        # يمكنك إضافة رسالة تحذير هنا إذا أردت
        pass # تجاهل الفلتر إذا لم يكن رقمًا صالحًا

    # التقسيم إلى صفحات
    paginator = Paginator(logs, 50)  # 50 سجل في الصفحة
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'title': _('سجلات الباركود'),
    }
    return render(request, 'inventory/barcode_logs.html', context)

@login_required
def scan_barcode(request):
    """عرض صفحة مسح الباركود ومعالجة الباركود الممسوح"""
    if request.method == 'POST':
        barcode_number = request.POST.get('barcode_number')
        if not barcode_number:
            return JsonResponse({'error': _('لم يتم توفير رقم باركود')}, status=400)

        try:
            barcode_entry = Barcode.objects.select_related('product').get(barcode_number=barcode_number)
            product = barcode_entry.product
            product_data = {
                'id': product.id,
                'name': product.name,
                'price': str(product.price), # تأكد من تحويل السعر إلى سلسلة إذا كان Decimal
                'quantity': product.quantity,
                # يمكنك إضافة المزيد من تفاصيل المنتج هنا
            }
            # تسجيل عملية المسح
            BarcodeLog.objects.create(
                barcode=barcode_entry,
                scanned_by=request.user,
                scan_type='product_lookup' # أو أي نوع آخر مناسب
            )
            return JsonResponse({'product': product_data})
        except Barcode.DoesNotExist:
            # تسجيل محاولة مسح باركود غير موجود
            # يمكنك اختيار تسجيل هذا أو لا بناءً على متطلباتك
            # BarcodeLog.objects.create(barcode_number_scanned=barcode_number, scanned_by=request.user, scan_type='not_found')
            return JsonResponse({'error': _('لم يتم العثور على منتج مرتبط بهذا الباركود')})
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    context = {
        'title': _('مسح الباركود'),
    }
    return render(request, 'inventory/scan_barcode.html', context)

@login_required
def bulk_print_barcodes(request):
    """طباعة باركودات متعددة"""
    if request.method == 'POST':
        try:
            product_ids = request.POST.getlist('product_ids')
            copies = int(request.POST.get('copies', 1))

            print(f"DEBUG: product_ids = {product_ids}, copies = {copies}")  # للتشخيص

            if not product_ids:
                messages.error(request, _('يرجى اختيار منتج واحد على الأقل'))
                return redirect('inventory:select_products_for_barcode')

            if copies < 1:
                copies = 1
            elif copies > 100:
                copies = 100
        except Exception as e:
            print(f"DEBUG: Error in bulk_print_barcodes: {e}")
            messages.error(request, f'خطأ في معالجة البيانات: {str(e)}')
            return redirect('inventory:select_products_for_barcode')

        # الحصول على الباركودات الأساسية للمنتجات المحددة
        barcodes_to_print = [] # تغيير اسم المتغير لتجنب التعارض
        for product_id in product_ids:
            product = get_object_or_404(Product, id=product_id)
            barcode_obj = Barcode.objects.filter(product=product, is_primary=True).first()

            if barcode_obj:
                # إنشاء الباركود
                try:
                    barcode_class = getattr(barcode, barcode_obj.barcode_type.code)
                    # writer options for better image quality
                    options = {
                        'module_height': 15.0,
                        'font_size': 10,
                        'text_distance': 5.0,
                        'quiet_zone': 2.0
                    }
                    barcode_instance = barcode_class(barcode_obj.barcode_number, writer=ImageWriter())

                    # إنشاء صورة الباركود
                    buffer = BytesIO()
                    barcode_instance.write(buffer, options=options)
                    barcode_image = base64.b64encode(buffer.getvalue()).decode('utf-8')

                    barcodes_to_print.append({
                        'barcode': barcode_obj,
                        'product': product,
                        'barcode_image': barcode_image,
                    })

                    # تسجيل عملية الطباعة
                    BarcodeLog.objects.create(
                        barcode=barcode_obj,
                        product=product,
                        action='print',
                        barcode_number=barcode_obj.barcode_number,
                        user=request.user,
                        details=_('تمت طباعة {} نسخة من الباركود ضمن طباعة متعددة').format(copies)
                    )
                except AttributeError:
                    messages.warning(request, _('نوع الباركود {} للمنتج {} غير صالح أو غير مدعوم.').format(barcode_obj.barcode_type.code, product.name))
                    continue # انتقل إلى المنتج التالي
                except Exception as e:
                    messages.error(request, _('حدث خطأ أثناء إنشاء باركود للمنتج {}: {}').format(product.name, str(e)))
                    continue # انتقل إلى المنتج التالي
            else:
                messages.warning(request, _('المنتج {} ليس لديه باركود أساسي.').format(product.name))

        if not barcodes_to_print:
            messages.error(request, _('لم يتم العثور على باركودات صالحة للطباعة.'))
            return redirect('inventory:select_products_for_barcode')

        settings = BarcodeSettings.get_settings()

        # إنشاء قائمة الباركودات مع النسخ
        final_barcodes = []
        for barcode_data in barcodes_to_print:
            for i in range(copies):
                final_barcodes.append(barcode_data)

        context = {
            'barcodes': final_barcodes,
            'original_barcodes': barcodes_to_print,
            'copies': copies,
            'settings': settings,
            'title': _('طباعة باركودات متعددة'),
        }
        return render(request, 'inventory/print_multiple_barcodes.html', context)

    # إذا لم تكن طريقة الطلب POST، إعادة توجيه إلى الصفحة الرئيسية
    return redirect('inventory:select_products_for_barcode')


@login_required
def barcode_import_export(request):
    """عرض صفحة استيراد وتصدير الباركود"""
    barcode_types = BarcodeType.objects.filter(is_active=True)
    products = Product.objects.filter(is_active=True)

    context = {
        'barcode_types': barcode_types,
        'products': products,
    }

    return render(request, 'inventory/barcode_import_export.html', context)