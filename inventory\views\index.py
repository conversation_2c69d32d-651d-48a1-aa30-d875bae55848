from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.utils.translation import gettext_lazy as _

from ..models import Product, Category

@login_required
def index(request):
    """
    عرض الصفحة الرئيسية لقسم المخزون
    """
    # الحصول على جميع المنتجات النشطة
    products = Product.objects.filter(is_active=True).order_by('name')
    
    # الحصول على جميع الفئات
    categories = Category.objects.all().order_by('name')
    
    context = {
        'products': products,
        'categories': categories,
        'title': _('المخزون'),
    }
    
    return render(request, 'inventory/index.html', context)