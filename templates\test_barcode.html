<!DOCTYPE html>
<html>
<head>
    <title>Test Barcode</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test Barcode AJAX</h1>
    <input type="text" id="barcodeInput" placeholder="Enter barcode" value="1282235856942741">
    <button onclick="testBarcode()">Test Barcode</button>
    <div id="result"></div>

    <script>
        // Get CSRF token from cookie
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        function testBarcode() {
            var barcode = $('#barcodeInput').val();
            const csrftoken = getCookie('csrftoken');
            
            console.log('Testing barcode:', barcode);
            console.log('CSRF Token:', csrftoken);
            
            $.ajax({
                url: "{% url 'inventory:barcode:scan_barcode' %}",
                type: 'POST',
                headers: {
                    'X-CSRFToken': csrftoken
                },
                data: {
                    'barcode_number': barcode
                },
                success: function(data) {
                    console.log('Success:', data);
                    $('#result').html('<div style="color: green; padding: 10px; border: 1px solid green;">Success: ' + JSON.stringify(data, null, 2) + '</div>');
                },
                error: function(xhr, status, error) {
                    console.log('Error:', xhr.responseText);
                    console.log('Status:', xhr.status);
                    $('#result').html('<div style="color: red; padding: 10px; border: 1px solid red;">Error (' + xhr.status + '): ' + xhr.responseText + '</div>');
                }
            });
        }
    </script>
</body>
</html>
