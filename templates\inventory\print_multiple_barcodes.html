{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "طباعة باركودات متعددة" %}{% endblock %}

{% block extra_css %}
<style>
    @media print {
        .no-print {
            display: none !important;
        }
        body {
            margin: 0;
            padding: 0;
        }
        .barcode-container {
            page-break-inside: avoid;
        }
        .barcode-page {
            page-break-after: always;
        }
        .barcode-page:last-child {
            page-break-after: auto;
        }
    }

    .barcode-container {
        display: grid;
        gap: var(--barcode-gap, 5px);
        margin: 20px 0;
        grid-template-columns: repeat(var(--columns-per-page, 3), 1fr);
    }

    .barcode-item {
        border: 1px solid #ddd;
        padding: var(--barcode-padding, 8px);
        text-align: center;
        background: white;
        border-radius: 3px;
        width: var(--barcode-width, 180px);
        height: var(--barcode-height, 120px);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin: auto;
    }

    .barcode-image {
        max-width: 100%;
        height: auto;
        flex-grow: 1;
        object-fit: contain;
    }

    .barcode-text {
        font-family: 'Courier New', monospace;
        font-size: var(--text-size, 10px);
        margin: 2px 0;
        word-break: break-all;
    }

    .product-name {
        font-weight: bold;
        font-size: var(--name-size, 11px);
        margin: 2px 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .product-price {
        color: #007bff;
        font-weight: bold;
        font-size: var(--price-size, 10px);
        margin: 2px 0;
    }

    .print-controls {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
    }

    .control-section {
        background: white;
        padding: 15px;
        border-radius: 5px;
        margin: 10px 0;
        border: 1px solid #e9ecef;
    }

    .control-row {
        display: flex;
        gap: 15px;
        align-items: center;
        margin: 10px 0;
        flex-wrap: wrap;
    }

    .control-group {
        display: flex;
        flex-direction: column;
        min-width: 120px;
    }

    .control-group label {
        font-size: 12px;
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }

    .control-group input, .control-group select {
        padding: 5px 8px;
        border: 1px solid #ced4da;
        border-radius: 3px;
        font-size: 13px;
    }

    .btn-group {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin: 15px 0;
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        font-size: 14px;
        cursor: pointer;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.2s;
    }

    .btn-primary {
        background: #007bff;
        color: white;
    }

    .btn-primary:hover {
        background: #0056b3;
    }

    .btn-success {
        background: #28a745;
        color: white;
    }

    .btn-success:hover {
        background: #1e7e34;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background: #545b62;
    }

    .print-info {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px;
        border-radius: 8px;
        margin: 20px 0;
        text-align: center;
    }

    .size-preset {
        display: inline-block;
        padding: 5px 10px;
        margin: 2px;
        background: #e9ecef;
        border: 1px solid #ced4da;
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
        transition: all 0.2s;
    }

    .size-preset:hover {
        background: #007bff;
        color: white;
    }

    .size-preset.active {
        background: #007bff;
        color: white;
    }

    .barcode-page {
        margin-bottom: 30px;
        padding: 10px;
        border: 1px dashed #ccc;
    }

    .page-header {
        text-align: center;
        font-size: 12px;
        color: #6c757d;
        margin-bottom: 10px;
        padding: 5px;
        background: #f8f9fa;
        border-radius: 3px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-print me-2"></i>{{ title }}
                    </h5>
                </div>
                <div class="card-body">
                    <!-- معلومات الطباعة -->
                    <div class="print-info no-print">
                        <h6><i class="fas fa-info-circle me-2"></i>{% trans "معلومات الطباعة" %}</h6>
                        <div class="row text-center">
                            <div class="col-md-3">
                                <strong>{{ original_barcodes|length }}</strong><br>
                                <small>{% trans "منتجات" %}</small>
                            </div>
                            <div class="col-md-3">
                                <strong id="current-copies">{{ copies }}</strong><br>
                                <small>{% trans "نسخ لكل منتج" %}</small>
                            </div>
                            <div class="col-md-3">
                                <strong id="total-barcodes">{{ barcodes|length }}</strong><br>
                                <small>{% trans "إجمالي الباركودات" %}</small>
                            </div>
                            <div class="col-md-3">
                                <strong id="total-pages">1</strong><br>
                                <small>{% trans "عدد الصفحات" %}</small>
                            </div>
                        </div>
                    </div>

                    <!-- أدوات التحكم في الطباعة -->
                    <div class="print-controls no-print">
                        <!-- إعدادات حجم الملصق -->
                        <div class="control-section">
                            <h6><i class="fas fa-ruler-combined me-2"></i>{% trans "حجم الملصق" %}</h6>
                            <div class="mb-3">
                                <div class="size-preset" data-width="{{ settings.label_width }}" data-height="{{ settings.label_height }}" data-name="محفوظ">{% trans "الإعداد المحفوظ" %} ({{ settings.label_width }}×{{ settings.label_height }})</div>
                                <div class="size-preset" data-width="30" data-height="20" data-name="صغير جداً">{% trans "صغير جداً" %} (30×20مم)</div>
                                <div class="size-preset" data-width="50" data-height="30" data-name="صغير">{% trans "صغير" %} (50×30مم)</div>
                                <div class="size-preset active" data-width="70" data-height="40" data-name="متوسط">{% trans "متوسط" %} (70×40مم)</div>
                                <div class="size-preset" data-width="100" data-height="60" data-name="كبير">{% trans "كبير" %} (100×60مم)</div>
                            </div>
                            <div class="control-row">
                                <div class="control-group">
                                    <label>{% trans "العرض (مم)" %}</label>
                                    <input type="number" id="label-width" value="{{ settings.label_width }}" min="20" max="200">
                                </div>
                                <div class="control-group">
                                    <label>{% trans "الارتفاع (مم)" %}</label>
                                    <input type="number" id="label-height" value="{{ settings.label_height }}" min="15" max="150">
                                </div>
                                <div class="control-group">
                                    <label>{% trans "المسافة بين الملصقات (مم)" %}</label>
                                    <input type="number" id="label-gap" value="2" min="0" max="10">
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات التخطيط -->
                        <div class="control-section">
                            <h6><i class="fas fa-th me-2"></i>{% trans "تخطيط الصفحة" %}</h6>
                            <div class="control-row">
                                <div class="control-group">
                                    <label>{% trans "عدد الملصقات في الصف" %}</label>
                                    <select id="labels-per-row">
                                        <option value="1" {% if settings.labels_per_row == 1 %}selected{% endif %}>1</option>
                                        <option value="2" {% if settings.labels_per_row == 2 %}selected{% endif %}>2</option>
                                        <option value="3" {% if settings.labels_per_row == 3 %}selected{% endif %}>3</option>
                                        <option value="4" {% if settings.labels_per_row == 4 %}selected{% endif %}>4</option>
                                        <option value="5" {% if settings.labels_per_row == 5 %}selected{% endif %}>5</option>
                                        <option value="6" {% if settings.labels_per_row == 6 %}selected{% endif %}>6</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label>{% trans "عدد الملصقات في العمود" %}</label>
                                    <select id="labels-per-column">
                                        <option value="5" {% if settings.labels_per_column == 5 %}selected{% endif %}>5</option>
                                        <option value="6" {% if settings.labels_per_column == 6 %}selected{% endif %}>6</option>
                                        <option value="8" {% if settings.labels_per_column == 8 %}selected{% endif %}>8</option>
                                        <option value="10" {% if settings.labels_per_column == 10 %}selected{% endif %}>10</option>
                                        <option value="12" {% if settings.labels_per_column == 12 %}selected{% endif %}>12</option>
                                        <option value="15" {% if settings.labels_per_column == 15 %}selected{% endif %}>15</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label>{% trans "نسخ إضافية لكل منتج" %}</label>
                                    <input type="number" id="extra-copies" value="0" min="0" max="50">
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات النص -->
                        <div class="control-section">
                            <h6><i class="fas fa-font me-2"></i>{% trans "إعدادات العرض" %}</h6>
                            <div class="control-row">
                                <div class="control-group">
                                    <label>{% trans "حجم الخط (بكسل)" %}</label>
                                    <input type="number" id="font-size" value="8" min="6" max="16">
                                </div>
                                <div class="control-group">
                                    <label>{% trans "إظهار اسم المنتج" %}</label>
                                    <select id="show-name">
                                        <option value="true" {% if settings.default_include_name %}selected{% endif %}>{% trans "نعم" %}</option>
                                        <option value="false" {% if not settings.default_include_name %}selected{% endif %}>{% trans "لا" %}</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label>{% trans "إظهار السعر" %}</label>
                                    <select id="show-price">
                                        <option value="true" {% if settings.default_include_price %}selected{% endif %}>{% trans "نعم" %}</option>
                                        <option value="false" {% if not settings.default_include_price %}selected{% endif %}>{% trans "لا" %}</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label>{% trans "إظهار رقم الباركود" %}</label>
                                    <select id="show-barcode-number">
                                        <option value="true" selected>{% trans "نعم" %}</option>
                                        <option value="false">{% trans "لا" %}</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="btn-group">
                            <button class="btn btn-primary" onclick="applySettings()">
                                <i class="fas fa-sync-alt"></i>{% trans "تطبيق الإعدادات" %}
                            </button>
                            <button class="btn btn-success" onclick="window.print()">
                                <i class="fas fa-print"></i>{% trans "طباعة الآن" %}
                            </button>
                            <a href="{% url 'inventory:barcode:barcode_settings' %}" class="btn btn-info" target="_blank">
                                <i class="fas fa-cog"></i>{% trans "إعدادات متقدمة" %}
                            </a>
                            <a href="{% url 'inventory:select_products_for_barcode' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i>{% trans "العودة" %}
                            </a>
                        </div>
                    </div>

                    <!-- الباركودات مقسمة على صفحات -->
                    <div id="barcode-pages">
                        {% if barcodes %}
                            <div class="barcode-page">
                                <div class="page-header no-print">
                                    <i class="fas fa-file-alt me-1"></i>{% trans "الصفحة" %} 1
                                </div>
                                <div class="barcode-container">
                                    {% for barcode_data in barcodes %}
                                        <div class="barcode-item">
                                            <div class="product-name">{{ barcode_data.product.name }}</div>
                                            <img src="data:image/png;base64,{{ barcode_data.barcode_image }}"
                                                 alt="Barcode" class="barcode-image">
                                            <div class="barcode-text">{{ barcode_data.barcode_number }}</div>
                                            <div class="product-price">{{ barcode_data.product.selling_price }} {% trans "د.م" %}</div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                                <h5>{% trans "لا توجد باركودات للطباعة" %}</h5>
                                <p class="text-muted">{% trans "لم يتم العثور على باركودات صالحة للطباعة" %}</p>
                                <a href="{% url 'inventory:select_products_for_barcode' %}" class="btn btn-primary">
                                    <i class="fas fa-arrow-left me-2"></i>{% trans "العودة لاختيار المنتجات" %}
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // بيانات الباركودات الأصلية من الخادم
    const originalProductsCount = {{ original_barcodes|length }};
    const originalCopies = {{ copies }};
    let originalBarcodes = [
        {% for barcode in original_barcodes %}
        {
            product: {
                name: "{{ barcode.product.name|escapejs }}",
                selling_price: "{{ barcode.product.selling_price }}"
            },
            barcode_image: "{{ barcode.barcode_image }}",
            barcode_number: "{{ barcode.barcode_number }}"
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];
    let currentBarcodes = [];

    // تهيئة البيانات الحالية
    function initializeCurrentBarcodes() {
        currentBarcodes = [];
        originalBarcodes.forEach(barcode => {
            for (let i = 0; i < originalCopies; i++) {
                currentBarcodes.push(barcode);
            }
        });
    }

    // تطبيق الإعدادات
    function applySettings() {
        const width = document.getElementById('label-width').value;
        const height = document.getElementById('label-height').value;
        const gap = document.getElementById('label-gap').value;
        const labelsPerRow = document.getElementById('labels-per-row').value;
        const labelsPerColumn = document.getElementById('labels-per-column').value;
        const extraCopies = parseInt(document.getElementById('extra-copies').value);
        const fontSize = document.getElementById('font-size').value;
        const showName = document.getElementById('show-name').value === 'true';
        const showPrice = document.getElementById('show-price').value === 'true';
        const showBarcodeNumber = document.getElementById('show-barcode-number').value === 'true';

        // تتبع إعدادات العرض
        console.log('إعدادات العرض:', { showName, showPrice, showBarcodeNumber });

        // تطبيق CSS variables بوحدة المليمتر
        const widthPx = Math.round(width * 3.78); // تحويل من مم إلى بكسل
        const heightPx = Math.round(height * 3.78);
        const gapPx = Math.round(gap * 3.78);

        document.documentElement.style.setProperty('--barcode-width', widthPx + 'px');
        document.documentElement.style.setProperty('--barcode-height', heightPx + 'px');
        document.documentElement.style.setProperty('--barcode-gap', gapPx + 'px');
        document.documentElement.style.setProperty('--columns-per-page', labelsPerRow);
        document.documentElement.style.setProperty('--font-size', fontSize + 'px');

        // إنشاء الباركودات مع النسخ الإضافية
        currentBarcodes = [];
        originalBarcodes.forEach(barcode => {
            const totalCopies = 1 + extraCopies;
            for (let i = 0; i < totalCopies; i++) {
                currentBarcodes.push(barcode);
            }
        });

        // إعادة تنظيم الصفحات
        reorganizePages(labelsPerRow, labelsPerColumn, showName, showPrice, showBarcodeNumber);

        // تحديث المعلومات
        updateInfo();
    }

    // إعادة تنظيم الباركودات في صفحات
    function reorganizePages(columns, rows, showName, showPrice, showBarcodeNumber) {
        console.log('إعادة تنظيم الصفحات:', { columns, rows, showName, showPrice, showBarcodeNumber });
        console.log('عدد الباركودات الحالية:', currentBarcodes.length);

        const itemsPerPage = columns * rows;
        const totalPages = Math.ceil(currentBarcodes.length / itemsPerPage);

        let pagesHTML = '';

        for (let page = 0; page < totalPages; page++) {
            const startIndex = page * itemsPerPage;
            const endIndex = Math.min(startIndex + itemsPerPage, currentBarcodes.length);
            const pageItems = currentBarcodes.slice(startIndex, endIndex);

            pagesHTML += `
                <div class="barcode-page">
                    <div class="page-header no-print">
                        <i class="fas fa-file-alt me-1"></i>{% trans "الصفحة" %} ${page + 1} {% trans "من" %} ${totalPages}
                    </div>
                    <div class="barcode-container">
            `;

            pageItems.forEach((barcode, index) => {
                const nameHTML = showName ?
                    `<div class="product-name">${barcode.product.name}</div>` : '';
                const priceHTML = showPrice ?
                    `<div class="product-price">${barcode.product.selling_price} {% trans "د.م" %}</div>` : '';
                const barcodeNumberHTML = showBarcodeNumber ?
                    `<div class="barcode-text">${barcode.barcode_number}</div>` : '';

                // تتبع للباركود الأول فقط
                if (index === 0) {
                    console.log('مثال على باركود:', {
                        name: barcode.product.name,
                        price: barcode.product.selling_price,
                        number: barcode.barcode_number,
                        showName: showName,
                        showPrice: showPrice,
                        showBarcodeNumber: showBarcodeNumber,
                        nameHTML: nameHTML,
                        priceHTML: priceHTML,
                        barcodeNumberHTML: barcodeNumberHTML
                    });
                    console.log('HTML النهائي للباركود:', `
                    <div class="barcode-item">
                        ${nameHTML}
                        <img src="data:image/png;base64,${barcode.barcode_image}" alt="Barcode" class="barcode-image">
                        ${barcodeNumberHTML}
                        ${priceHTML}
                    </div>
                    `);
                }

                pagesHTML += `
                    <div class="barcode-item">
                        ${nameHTML}
                        <img src="data:image/png;base64,${barcode.barcode_image}" alt="Barcode" class="barcode-image">
                        ${barcodeNumberHTML}
                        ${priceHTML}
                    </div>
                `;
            });

            pagesHTML += `
                    </div>
                </div>
            `;
        }

        document.getElementById('barcode-pages').innerHTML = pagesHTML;
    }

    // تحديث المعلومات
    function updateInfo() {
        const extraCopies = parseInt(document.getElementById('extra-copies').value);
        const totalCopies = 1 + extraCopies;
        const labelsPerRow = parseInt(document.getElementById('labels-per-row').value);
        const labelsPerColumn = parseInt(document.getElementById('labels-per-column').value);
        const itemsPerPage = labelsPerRow * labelsPerColumn;
        const totalPages = Math.ceil(currentBarcodes.length / itemsPerPage);

        document.getElementById('current-copies').textContent = totalCopies;
        document.getElementById('total-barcodes').textContent = currentBarcodes.length;
        document.getElementById('total-pages').textContent = totalPages;
    }

    // معالجات الأحداث للأحجام المحددة مسبقاً
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.size-preset').forEach(preset => {
            preset.addEventListener('click', function() {
                // إزالة التحديد من جميع الأحجام
                document.querySelectorAll('.size-preset').forEach(p => p.classList.remove('active'));
                // تحديد الحجم الحالي
                this.classList.add('active');

                // تطبيق القيم
                document.getElementById('label-width').value = this.dataset.width;
                document.getElementById('label-height').value = this.dataset.height;

                // تطبيق الإعدادات
                applySettings();
            });
        });

        // معالجات الأحداث للتحديث التلقائي
        ['label-width', 'label-height', 'label-gap', 'labels-per-row', 'labels-per-column',
         'extra-copies', 'font-size', 'show-name', 'show-price', 'show-barcode-number'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', applySettings);
            }
        });

        // تتبع البيانات الأصلية
        console.log('البيانات الأصلية:', originalBarcodes);
        console.log('عدد الباركودات الأصلية:', originalBarcodes.length);
        if (originalBarcodes.length > 0) {
            console.log('مثال على باركود أصلي:', originalBarcodes[0]);
        }

        // تهيئة البيانات الحالية
        initializeCurrentBarcodes();

        // تطبيق الإعدادات الافتراضية
        applySettings();
    });

    // تحسين الطباعة
    window.addEventListener('beforeprint', function() {
        document.title = '{% trans "طباعة باركودات" %} - ' + (currentBarcodes.length || {{ barcodes|length }}) + ' {% trans "باركود" %}';
    });

    window.addEventListener('afterprint', function() {
        document.title = '{{ title }}';
    });

    // اختصار لوحة المفاتيح للطباعة
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            window.print();
        }
        if (e.ctrlKey && e.key === 'Enter') {
            e.preventDefault();
            applySettings();
        }
    });
</script>
{% endblock %}
