{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "طباعة باركودات متعددة" %}{% endblock %}

{% block extra_css %}
<style>
    @media print {
        .no-print {
            display: none !important;
        }
        body {
            margin: 0;
            padding: 0;
        }
        .barcode-container {
            page-break-inside: avoid;
        }
        .barcode-page {
            page-break-after: always;
        }
        .barcode-page:last-child {
            page-break-after: auto;
        }
    }

    .barcode-container {
        display: grid;
        gap: var(--barcode-gap, 5px);
        margin: 20px 0;
        grid-template-columns: repeat(var(--columns-per-page, 3), 1fr);
    }

    .barcode-item {
        border: 1px solid #ddd;
        padding: var(--barcode-padding, 8px);
        text-align: center;
        background: white;
        border-radius: 3px;
        width: var(--barcode-width, 180px);
        height: var(--barcode-height, 120px);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin: auto;
    }

    .barcode-image {
        max-width: 100%;
        height: auto;
        flex-grow: 1;
        object-fit: contain;
    }

    .barcode-text {
        font-family: 'Courier New', monospace;
        font-size: var(--text-size, 10px);
        margin: 2px 0;
        word-break: break-all;
    }

    .product-name {
        font-weight: bold;
        font-size: var(--name-size, 11px);
        margin: 2px 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .product-price {
        color: #007bff;
        font-weight: bold;
        font-size: var(--price-size, 10px);
        margin: 2px 0;
    }

    .print-controls {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
    }

    .control-section {
        background: white;
        padding: 15px;
        border-radius: 5px;
        margin: 10px 0;
        border: 1px solid #e9ecef;
    }

    .control-row {
        display: flex;
        gap: 15px;
        align-items: center;
        margin: 10px 0;
        flex-wrap: wrap;
    }

    .control-group {
        display: flex;
        flex-direction: column;
        min-width: 120px;
    }

    .control-group label {
        font-size: 12px;
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }

    .control-group input, .control-group select {
        padding: 5px 8px;
        border: 1px solid #ced4da;
        border-radius: 3px;
        font-size: 13px;
    }

    .btn-group {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin: 15px 0;
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        font-size: 14px;
        cursor: pointer;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.2s;
    }

    .btn-primary {
        background: #007bff;
        color: white;
    }

    .btn-primary:hover {
        background: #0056b3;
    }

    .btn-success {
        background: #28a745;
        color: white;
    }

    .btn-success:hover {
        background: #1e7e34;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background: #545b62;
    }

    .print-info {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px;
        border-radius: 8px;
        margin: 20px 0;
        text-align: center;
    }

    .size-preset {
        display: inline-block;
        padding: 5px 10px;
        margin: 2px;
        background: #e9ecef;
        border: 1px solid #ced4da;
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
        transition: all 0.2s;
    }

    .size-preset:hover {
        background: #007bff;
        color: white;
    }

    .size-preset.active {
        background: #007bff;
        color: white;
    }

    .barcode-page {
        margin-bottom: 30px;
        padding: 10px;
        border: 1px dashed #ccc;
    }

    .page-header {
        text-align: center;
        font-size: 12px;
        color: #6c757d;
        margin-bottom: 10px;
        padding: 5px;
        background: #f8f9fa;
        border-radius: 3px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-print me-2"></i>{{ title }}
                    </h5>
                </div>
                <div class="card-body">
                    <!-- معلومات الطباعة -->
                    <div class="print-info no-print">
                        <h6><i class="fas fa-info-circle me-2"></i>{% trans "معلومات الطباعة" %}</h6>
                        <div class="row text-center">
                            <div class="col-md-3">
                                <strong>{{ original_barcodes|length }}</strong><br>
                                <small>{% trans "منتجات" %}</small>
                            </div>
                            <div class="col-md-3">
                                <strong id="current-copies">{{ copies }}</strong><br>
                                <small>{% trans "نسخ لكل منتج" %}</small>
                            </div>
                            <div class="col-md-3">
                                <strong id="total-barcodes">{{ barcodes|length }}</strong><br>
                                <small>{% trans "إجمالي الباركودات" %}</small>
                            </div>
                            <div class="col-md-3">
                                <strong id="total-pages">1</strong><br>
                                <small>{% trans "عدد الصفحات" %}</small>
                            </div>
                        </div>
                    </div>

                    <!-- أدوات التحكم في الطباعة -->
                    <div class="print-controls no-print">
                        <!-- إعدادات حجم الملصق -->
                        <div class="control-section">
                            <h6><i class="fas fa-ruler-combined me-2"></i>{% trans "حجم الملصق" %}</h6>
                            <div class="mb-3">
                                <div class="size-preset active" data-width="120" data-height="80" data-name="صغير">{% trans "صغير" %} (120×80)</div>
                                <div class="size-preset" data-width="180" data-height="120" data-name="متوسط">{% trans "متوسط" %} (180×120)</div>
                                <div class="size-preset" data-width="240" data-height="160" data-name="كبير">{% trans "كبير" %} (240×160)</div>
                                <div class="size-preset" data-width="300" data-height="200" data-name="كبير جداً">{% trans "كبير جداً" %} (300×200)</div>
                            </div>
                            <div class="control-row">
                                <div class="control-group">
                                    <label>{% trans "العرض (بكسل)" %}</label>
                                    <input type="number" id="barcode-width" value="180" min="80" max="400">
                                </div>
                                <div class="control-group">
                                    <label>{% trans "الارتفاع (بكسل)" %}</label>
                                    <input type="number" id="barcode-height" value="120" min="60" max="300">
                                </div>
                                <div class="control-group">
                                    <label>{% trans "المسافة بين الملصقات" %}</label>
                                    <input type="number" id="barcode-gap" value="5" min="0" max="20">
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات التخطيط -->
                        <div class="control-section">
                            <h6><i class="fas fa-th me-2"></i>{% trans "تخطيط الصفحة" %}</h6>
                            <div class="control-row">
                                <div class="control-group">
                                    <label>{% trans "عدد الأعمدة في الصفحة" %}</label>
                                    <select id="columns-per-page">
                                        <option value="1">1</option>
                                        <option value="2">2</option>
                                        <option value="3" selected>3</option>
                                        <option value="4">4</option>
                                        <option value="5">5</option>
                                        <option value="6">6</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label>{% trans "عدد الصفوف في الصفحة" %}</label>
                                    <select id="rows-per-page">
                                        <option value="3">3</option>
                                        <option value="4">4</option>
                                        <option value="5" selected>5</option>
                                        <option value="6">6</option>
                                        <option value="8">8</option>
                                        <option value="10">10</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label>{% trans "نسخ إضافية لكل منتج" %}</label>
                                    <input type="number" id="extra-copies" value="0" min="0" max="20">
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات النص -->
                        <div class="control-section">
                            <h6><i class="fas fa-font me-2"></i>{% trans "إعدادات النص" %}</h6>
                            <div class="control-row">
                                <div class="control-group">
                                    <label>{% trans "حجم اسم المنتج" %}</label>
                                    <input type="number" id="name-size" value="11" min="8" max="20">
                                </div>
                                <div class="control-group">
                                    <label>{% trans "حجم رقم الباركود" %}</label>
                                    <input type="number" id="text-size" value="10" min="6" max="16">
                                </div>
                                <div class="control-group">
                                    <label>{% trans "حجم السعر" %}</label>
                                    <input type="number" id="price-size" value="10" min="6" max="16">
                                </div>
                                <div class="control-group">
                                    <label>{% trans "إظهار السعر" %}</label>
                                    <select id="show-price">
                                        <option value="true" selected>{% trans "نعم" %}</option>
                                        <option value="false">{% trans "لا" %}</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="btn-group">
                            <button class="btn btn-primary" onclick="applySettings()">
                                <i class="fas fa-sync-alt"></i>{% trans "تطبيق الإعدادات" %}
                            </button>
                            <button class="btn btn-success" onclick="window.print()">
                                <i class="fas fa-print"></i>{% trans "طباعة الآن" %}
                            </button>
                            <a href="{% url 'inventory:select_products_for_barcode' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i>{% trans "العودة" %}
                            </a>
                        </div>
                    </div>

                    <!-- الباركودات مقسمة على صفحات -->
                    <div id="barcode-pages">
                        {% if barcodes %}
                            <div class="barcode-page">
                                <div class="page-header no-print">
                                    <i class="fas fa-file-alt me-1"></i>{% trans "الصفحة" %} 1
                                </div>
                                <div class="barcode-container">
                                    {% for barcode_data in barcodes %}
                                        <div class="barcode-item">
                                            <div class="product-name">{{ barcode_data.product.name }}</div>
                                            <img src="data:image/png;base64,{{ barcode_data.barcode_image }}"
                                                 alt="Barcode" class="barcode-image">
                                            <div class="barcode-text">{{ barcode_data.barcode_number }}</div>
                                            <div class="product-price">{{ barcode_data.product.selling_price }} {% trans "ج.م" %}</div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                                <h5>{% trans "لا توجد باركودات للطباعة" %}</h5>
                                <p class="text-muted">{% trans "لم يتم العثور على باركودات صالحة للطباعة" %}</p>
                                <a href="{% url 'inventory:select_products_for_barcode' %}" class="btn btn-primary">
                                    <i class="fas fa-arrow-left me-2"></i>{% trans "العودة لاختيار المنتجات" %}
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // بيانات الباركودات الأصلية
    const originalProductsCount = {{ original_barcodes|length }};
    const originalCopies = {{ copies }};
    let currentBarcodes = [];

    // جمع بيانات الباركودات من DOM
    function collectOriginalBarcodes() {
        const barcodes = [];
        document.querySelectorAll('.barcode-item').forEach(item => {
            const name = item.querySelector('.product-name').textContent;
            const image = item.querySelector('.barcode-image').src;
            const number = item.querySelector('.barcode-text').textContent;
            const priceElement = item.querySelector('.product-price');
            const price = priceElement ? priceElement.textContent.replace(' ج.م', '') : '0';

            barcodes.push({
                product: { name: name, selling_price: price },
                barcode_image: image.split(',')[1], // إزالة data:image/png;base64,
                barcode_number: number
            });
        });
        return barcodes;
    }

    // تطبيق الإعدادات
    function applySettings() {
        const width = document.getElementById('barcode-width').value;
        const height = document.getElementById('barcode-height').value;
        const gap = document.getElementById('barcode-gap').value;
        const columns = document.getElementById('columns-per-page').value;
        const rows = document.getElementById('rows-per-page').value;
        const extraCopies = parseInt(document.getElementById('extra-copies').value);
        const nameSize = document.getElementById('name-size').value;
        const textSize = document.getElementById('text-size').value;
        const priceSize = document.getElementById('price-size').value;
        const showPrice = document.getElementById('show-price').value === 'true';

        // تطبيق CSS variables
        document.documentElement.style.setProperty('--barcode-width', width + 'px');
        document.documentElement.style.setProperty('--barcode-height', height + 'px');
        document.documentElement.style.setProperty('--barcode-gap', gap + 'px');
        document.documentElement.style.setProperty('--columns-per-page', columns);
        document.documentElement.style.setProperty('--name-size', nameSize + 'px');
        document.documentElement.style.setProperty('--text-size', textSize + 'px');
        document.documentElement.style.setProperty('--price-size', priceSize + 'px');

        // إنشاء الباركودات مع النسخ الإضافية
        currentBarcodes = [];
        originalBarcodes.forEach(barcode => {
            const totalCopies = 1 + extraCopies;
            for (let i = 0; i < totalCopies; i++) {
                currentBarcodes.push(barcode);
            }
        });

        // إعادة تنظيم الصفحات
        reorganizePages(columns, rows, showPrice);

        // تحديث المعلومات
        updateInfo();
    }

    // إعادة تنظيم الباركودات في صفحات
    function reorganizePages(columns, rows, showPrice) {
        const itemsPerPage = columns * rows;
        const totalPages = Math.ceil(currentBarcodes.length / itemsPerPage);

        let pagesHTML = '';

        for (let page = 0; page < totalPages; page++) {
            const startIndex = page * itemsPerPage;
            const endIndex = Math.min(startIndex + itemsPerPage, currentBarcodes.length);
            const pageItems = currentBarcodes.slice(startIndex, endIndex);

            pagesHTML += `
                <div class="barcode-page">
                    <div class="page-header no-print">
                        <i class="fas fa-file-alt me-1"></i>{% trans "الصفحة" %} ${page + 1} {% trans "من" %} ${totalPages}
                    </div>
                    <div class="barcode-container">
            `;

            pageItems.forEach(barcode => {
                const priceHTML = showPrice ?
                    `<div class="product-price">${barcode.product.selling_price} {% trans "ج.م" %}</div>` : '';

                pagesHTML += `
                    <div class="barcode-item">
                        <div class="product-name">${barcode.product.name}</div>
                        <img src="data:image/png;base64,${barcode.barcode_image}" alt="Barcode" class="barcode-image">
                        <div class="barcode-text">${barcode.barcode_number}</div>
                        ${priceHTML}
                    </div>
                `;
            });

            pagesHTML += `
                    </div>
                </div>
            `;
        }

        document.getElementById('barcode-pages').innerHTML = pagesHTML;
    }

    // تحديث المعلومات
    function updateInfo() {
        const extraCopies = parseInt(document.getElementById('extra-copies').value);
        const totalCopies = 1 + extraCopies;
        const columns = parseInt(document.getElementById('columns-per-page').value);
        const rows = parseInt(document.getElementById('rows-per-page').value);
        const itemsPerPage = columns * rows;
        const totalPages = Math.ceil(currentBarcodes.length / itemsPerPage);

        document.getElementById('current-copies').textContent = totalCopies;
        document.getElementById('total-barcodes').textContent = currentBarcodes.length;
        document.getElementById('total-pages').textContent = totalPages;
    }

    // معالجات الأحداث للأحجام المحددة مسبقاً
    document.querySelectorAll('.size-preset').forEach(preset => {
        preset.addEventListener('click', function() {
            // إزالة التحديد من جميع الأحجام
            document.querySelectorAll('.size-preset').forEach(p => p.classList.remove('active'));
            // تحديد الحجم الحالي
            this.classList.add('active');

            // تطبيق القيم
            document.getElementById('barcode-width').value = this.dataset.width;
            document.getElementById('barcode-height').value = this.dataset.height;

            // تطبيق الإعدادات
            applySettings();
        });
    });

    // معالجات الأحداث للتحديث التلقائي
    ['barcode-width', 'barcode-height', 'barcode-gap', 'columns-per-page', 'rows-per-page',
     'extra-copies', 'name-size', 'text-size', 'price-size', 'show-price'].forEach(id => {
        document.getElementById(id).addEventListener('change', applySettings);
    });

    // تحسين الطباعة
    window.addEventListener('beforeprint', function() {
        document.title = '{% trans "طباعة باركودات" %} - ' + currentBarcodes.length + ' {% trans "باركود" %}';
        // إخفاء رؤوس الصفحات عند الطباعة
        document.querySelectorAll('.page-header').forEach(header => {
            header.style.display = 'none';
        });
    });

    window.addEventListener('afterprint', function() {
        document.title = '{{ title }}';
        // إظهار رؤوس الصفحات بعد الطباعة
        document.querySelectorAll('.page-header').forEach(header => {
            header.style.display = 'block';
        });
    });

    // اختصار لوحة المفاتيح للطباعة
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            window.print();
        }
        // اختصار لتطبيق الإعدادات
        if (e.ctrlKey && e.key === 'Enter') {
            e.preventDefault();
            applySettings();
        }
    });

    // تطبيق الإعدادات الافتراضية عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        applySettings();
    });

    // حفظ الإعدادات في localStorage
    function saveSettings() {
        const settings = {
            width: document.getElementById('barcode-width').value,
            height: document.getElementById('barcode-height').value,
            gap: document.getElementById('barcode-gap').value,
            columns: document.getElementById('columns-per-page').value,
            rows: document.getElementById('rows-per-page').value,
            nameSize: document.getElementById('name-size').value,
            textSize: document.getElementById('text-size').value,
            priceSize: document.getElementById('price-size').value,
            showPrice: document.getElementById('show-price').value
        };
        localStorage.setItem('barcodeSettings', JSON.stringify(settings));
    }

    // تحميل الإعدادات من localStorage
    function loadSettings() {
        const saved = localStorage.getItem('barcodeSettings');
        if (saved) {
            const settings = JSON.parse(saved);
            document.getElementById('barcode-width').value = settings.width || 180;
            document.getElementById('barcode-height').value = settings.height || 120;
            document.getElementById('barcode-gap').value = settings.gap || 5;
            document.getElementById('columns-per-page').value = settings.columns || 3;
            document.getElementById('rows-per-page').value = settings.rows || 5;
            document.getElementById('name-size').value = settings.nameSize || 11;
            document.getElementById('text-size').value = settings.textSize || 10;
            document.getElementById('price-size').value = settings.priceSize || 10;
            document.getElementById('show-price').value = settings.showPrice || 'true';
        }
    }

    // حفظ الإعدادات عند التغيير
    ['barcode-width', 'barcode-height', 'barcode-gap', 'columns-per-page', 'rows-per-page',
     'name-size', 'text-size', 'price-size', 'show-price'].forEach(id => {
        document.getElementById(id).addEventListener('change', saveSettings);
    });

    // تحميل الإعدادات عند بدء التشغيل
    loadSettings();
</script>
{% endblock %}
