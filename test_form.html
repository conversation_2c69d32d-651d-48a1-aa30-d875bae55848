<!DOCTYPE html>
<html>
<head>
    <title>اختبار النموذج</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>اختبار زر الطباعة</h1>
    
    <form action="http://127.0.0.1:8000/inventory/barcode/bulk-print/" method="post">
        <input type="hidden" name="csrfmiddlewaretoken" value="test">
        
        <label>
            <input type="checkbox" name="product_ids" value="1" class="product-checkbox"> منتج 1
        </label><br>
        
        <label>
            <input type="checkbox" name="product_ids" value="2" class="product-checkbox"> منتج 2
        </label><br>
        
        <label>عدد النسخ:</label>
        <input type="number" name="copies" value="1" min="1" max="100"><br><br>
        
        <button type="submit" id="print_button">طباعة الباركودات</button>
    </form>
    
    <script>
        $('#print_button').on('click', function(e) {
            console.log('Print button clicked');
            var checkedProducts = $('.product-checkbox:checked').length;
            console.log('Checked products:', checkedProducts);
            
            if (checkedProducts === 0) {
                e.preventDefault();
                alert('يرجى اختيار منتج واحد على الأقل');
                return false;
            }
            
            console.log('Form will be submitted');
        });
    </script>
</body>
</html>
