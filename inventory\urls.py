from django.urls import path, include
from . import views

urlpatterns = [
    path('', views.index, name='index'),
    path('add/', views.add_product, name='add_product'),
    path('edit/<int:product_id>/', views.edit_product, name='edit_product'),
    path('delete/<int:product_id>/', views.delete_product, name='delete_product'),
    path('product/<int:product_id>/', views.product_detail, name='product_detail'),
    path('product/<int:product_id>/print-barcode/', views.print_barcode, name='print_barcode'),
    path('product/<int:product_id>/movements/', views.get_product_movements, name='get_product_movements'),
    path('product/<int:product_id>/details/', views.product_details_json, name='product_details_json'),
    path('product/<int:product_id>/add-movement/', views.add_movement, name='add_movement'),

    path('categories/', views.categories, name='categories'),
    path('categories/add/', views.add_category, name='add_category'),
    path('categories/edit/<int:category_id>/', views.edit_category, name='edit_category'),
    path('categories/delete/<int:category_id>/', views.delete_category, name='delete_category'),
    path('categories/add-ajax/', views.add_category_ajax, name='add_category_ajax'),

    path('storage-locations/', views.storage_locations, name='storage_locations'),
    path('storage-locations/add/', views.add_storage_location, name='add_storage_location'),
    path('storage-locations/edit/<int:location_id>/', views.edit_storage_location, name='edit_storage_location'),
    path('storage-locations/delete/<int:location_id>/', views.delete_storage_location, name='delete_storage_location'),
    path('storage-locations/add-ajax/', views.add_storage_location_ajax, name='add_storage_location_ajax'),

    path('search/', views.search_products, name='search_products'),
    path('export/', views.export_products, name='export_products'),
    path('export-template/', views.export_template, name='export_template'),
    path('import/', views.import_products, name='import_products'),
    path('bulk-delete/', views.bulk_delete, name='bulk_delete'),
    path('stock-movement/add/', views.stock_movement_ajax, name='stock_movement_ajax'),
    path('alerts/', views.stock_alerts, name='stock_alerts'),
    path('create-purchase-order/', views.create_purchase_order, name='create_purchase_order'),
    
    # إضافة مسارات الباركود
    path('barcode/', include('inventory.barcode_urls')),
]
