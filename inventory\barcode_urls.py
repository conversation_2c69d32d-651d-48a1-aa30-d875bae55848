from django.urls import path
from . import barcode_views
from . import barcode_dashboard_views
from .views import select_products_for_barcode

urlpatterns = [
    # لوحة تحكم الباركود المتكاملة
    path('dashboard/', barcode_dashboard_views.barcode_dashboard, name='barcode_dashboard'),

    # إعدادات الباركود
    path('settings/', barcode_views.barcode_settings, name='barcode_settings'),

    # أنواع الباركود
    path('types/', barcode_views.barcode_types, name='barcode_types'),
    path('types/add/', barcode_views.add_barcode_type, name='add_barcode_type'),
    path('types/edit/<int:type_id>/', barcode_views.edit_barcode_type, name='edit_barcode_type'),
    path('types/delete/<int:type_id>/', barcode_views.delete_barcode_type, name='delete_barcode_type'),

    # باركودات المنتج
    path('product/<int:product_id>/barcodes/', barcode_views.product_barcodes, name='product_barcodes'),
    path('product/<int:product_id>/barcodes/add/', barcode_views.add_product_barcode, name='add_product_barcode'),
    path('<int:barcode_id>/edit/', barcode_views.edit_product_barcode, name='edit_product_barcode'),
    path('<int:barcode_id>/delete/', barcode_views.delete_product_barcode, name='delete_product_barcode'),
    path('<int:barcode_id>/print/', barcode_views.print_product_barcode, name='print_product_barcode'),

    # توليد وطباعة الباركودات
    path('generate/', barcode_views.generate_barcode, name='generate_barcode'),
    path('select-products/', select_products_for_barcode, name='select_products_for_barcode'),
    path('bulk-print/', barcode_views.bulk_print_barcodes, name='bulk_print_barcodes'),

    # مسح الباركود
    path('scan/', barcode_views.scan_barcode, name='scan_barcode'),

    # سجلات الباركود
    path('logs/', barcode_views.barcode_logs, name='barcode_logs'),

    # استيراد وتصدير الباركود
    path('import-export/', barcode_views.barcode_import_export, name='barcode_import_export'),
]