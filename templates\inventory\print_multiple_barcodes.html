{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "طباعة باركودات متعددة" %}{% endblock %}

{% block extra_css %}
<style>
    @media print {
        .no-print {
            display: none !important;
        }
        body {
            margin: 0;
            padding: 0;
        }
        .barcode-container {
            page-break-inside: avoid;
        }
    }

    .barcode-container {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        justify-content: center;
        margin: 20px 0;
    }

    .barcode-item {
        border: 1px solid #ddd;
        padding: 10px;
        text-align: center;
        background: white;
        border-radius: 5px;
        width: 200px;
        margin: 5px;
    }

    .barcode-image {
        max-width: 100%;
        height: auto;
        margin: 10px 0;
    }

    .barcode-text {
        font-family: 'Courier New', monospace;
        font-size: 12px;
        margin: 5px 0;
    }

    .product-name {
        font-weight: bold;
        font-size: 14px;
        margin: 5px 0;
    }

    .product-price {
        color: #007bff;
        font-weight: bold;
        font-size: 13px;
    }

    .print-controls {
        text-align: center;
        margin: 20px 0;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 5px;
    }

    .btn-print {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        font-size: 16px;
        cursor: pointer;
        margin: 0 10px;
    }

    .btn-print:hover {
        background: #0056b3;
    }

    .btn-back {
        background: #6c757d;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        font-size: 16px;
        cursor: pointer;
        margin: 0 10px;
        text-decoration: none;
        display: inline-block;
    }

    .btn-back:hover {
        background: #545b62;
        color: white;
        text-decoration: none;
    }

    .print-info {
        background: #e9ecef;
        padding: 15px;
        border-radius: 5px;
        margin: 20px 0;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-print me-2"></i>{{ title }}
                    </h5>
                </div>
                <div class="card-body">
                    <!-- معلومات الطباعة -->
                    <div class="print-info no-print">
                        <h6>{% trans "معلومات الطباعة" %}</h6>
                        <p>
                            {% trans "عدد المنتجات" %}: <strong>{{ original_barcodes|length }}</strong> |
                            {% trans "عدد النسخ لكل منتج" %}: <strong>{{ copies }}</strong> |
                            {% trans "إجمالي الباركودات" %}: <strong>{{ barcodes|length }}</strong>
                        </p>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="print-controls no-print">
                        <button class="btn-print" onclick="window.print()">
                            <i class="fas fa-print me-2"></i>{% trans "طباعة الآن" %}
                        </button>
                        <a href="{% url 'inventory:select_products_for_barcode' %}" class="btn-back">
                            <i class="fas fa-arrow-left me-2"></i>{% trans "العودة" %}
                        </a>
                    </div>

                    <!-- الباركودات -->
                    <div class="barcode-container">
                        {% for barcode_data in barcodes %}
                            <div class="barcode-item">
                                <div class="product-name">{{ barcode_data.product.name }}</div>
                                <img src="data:image/png;base64,{{ barcode_data.barcode_image }}"
                                     alt="Barcode" class="barcode-image">
                                <div class="barcode-text">{{ barcode_data.barcode_number }}</div>
                                <div class="product-price">{{ barcode_data.product.selling_price }} {% trans "ج.م" %}</div>
                            </div>
                        {% empty %}
                            <div class="col-12 text-center py-5">
                                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                                <h5>{% trans "لا توجد باركودات للطباعة" %}</h5>
                                <p class="text-muted">{% trans "لم يتم العثور على باركودات صالحة للطباعة" %}</p>
                                <a href="{% url 'inventory:select_products_for_barcode' %}" class="btn btn-primary">
                                    <i class="fas fa-arrow-left me-2"></i>{% trans "العودة لاختيار المنتجات" %}
                                </a>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحسين الطباعة
    window.addEventListener('beforeprint', function() {
        document.title = '{% trans "طباعة باركودات" %} - {{ barcodes|length }} {% trans "منتج" %}';
    });

    window.addEventListener('afterprint', function() {
        document.title = '{{ title }}';
    });

    // اختصار لوحة المفاتيح للطباعة
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            window.print();
        }
    });
</script>
{% endblock %}
