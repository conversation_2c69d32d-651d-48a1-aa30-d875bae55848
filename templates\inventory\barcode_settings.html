{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<style>
    .settings-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
    .settings-card .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        padding: 15px 20px;
    }
    .settings-card .card-body {
        padding: 20px;
    }
    .form-group label {
        font-weight: 600;
    }
    .help-text {
        font-size: 0.85rem;
        color: #6c757d;
        margin-top: 5px;
    }
    .preview-container {
        border: 1px dashed #ced4da;
        padding: 15px;
        text-align: center;
        margin-top: 10px;
        border-radius: 5px;
    }
    .barcode-preview {
        max-width: 100%;
        height: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'dashboard:index' %}">{% trans "لوحة التحكم" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'inventory:index' %}">{% trans "المخزون" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "إعدادات الباركود" %}</li>
                    </ol>
                </div>
                <h4 class="page-title">{% trans "إعدادات الباركود" %}</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <form method="post" action="{% url 'inventory:barcode_settings' %}">
                {% csrf_token %}

                <!-- إعدادات الباركود الافتراضية -->
                <div class="card settings-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">{% trans "الإعدادات الافتراضية" %}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="default_barcode_type">{% trans "نوع الباركود الافتراضي" %}</label>
                                    <select class="form-control" id="default_barcode_type" name="default_barcode_type">
                                        <option value="">{% trans "اختر نوع الباركود" %}</option>
                                        {% for barcode_type in barcode_types %}
                                            <option value="{{ barcode_type.id }}" {% if settings.default_barcode_type_id == barcode_type.id %}selected{% endif %}>
                                                {{ barcode_type.name }} ({{ barcode_type.code }})
                                            </option>
                                        {% endfor %}
                                    </select>
                                    <div class="help-text">{% trans "نوع الباركود الذي سيتم استخدامه افتراضيًا عند إنشاء باركود جديد" %}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="d-block">{% trans "خيارات العرض الافتراضية" %}</label>
                                    <div class="custom-control custom-switch mb-2">
                                        <input type="checkbox" class="custom-control-input" id="default_include_price" name="default_include_price" {% if settings.default_include_price %}checked{% endif %}>
                                        <label class="custom-control-label" for="default_include_price">{% trans "تضمين السعر في الباركود" %}</label>
                                    </div>
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="default_include_name" name="default_include_name" {% if settings.default_include_name %}checked{% endif %}>
                                        <label class="custom-control-label" for="default_include_name">{% trans "تضمين اسم المنتج في الباركود" %}</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إعدادات أبعاد الملصق -->
                <div class="card settings-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">{% trans "أبعاد الملصق" %}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="label_width">{% trans "عرض الملصق (مم)" %}</label>
                                    <input type="number" class="form-control" id="label_width" name="label_width" value="{{ settings.label_width }}" min="10" max="200">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="label_height">{% trans "ارتفاع الملصق (مم)" %}</label>
                                    <input type="number" class="form-control" id="label_height" name="label_height" value="{{ settings.label_height }}" min="10" max="200">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="labels_per_row">{% trans "عدد الملصقات في الصف" %}</label>
                                    <input type="number" class="form-control" id="labels_per_row" name="labels_per_row" value="{{ settings.labels_per_row }}" min="1" max="10">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="labels_per_column">{% trans "عدد الملصقات في العمود" %}</label>
                                    <input type="number" class="form-control" id="labels_per_column" name="labels_per_column" value="{{ settings.labels_per_column }}" min="1" max="20">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الهوامش -->
                <div class="card settings-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">{% trans "الهوامش" %}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="margin_top">{% trans "الهامش العلوي (مم)" %}</label>
                                    <input type="number" class="form-control" id="margin_top" name="margin_top" value="{{ settings.margin_top }}" min="0" max="50">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="margin_right">{% trans "الهامش الأيمن (مم)" %}</label>
                                    <input type="number" class="form-control" id="margin_right" name="margin_right" value="{{ settings.margin_right }}" min="0" max="50">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="margin_bottom">{% trans "الهامش السفلي (مم)" %}</label>
                                    <input type="number" class="form-control" id="margin_bottom" name="margin_bottom" value="{{ settings.margin_bottom }}" min="0" max="50">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="margin_left">{% trans "الهامش الأيسر (مم)" %}</label>
                                    <input type="number" class="form-control" id="margin_left" name="margin_left" value="{{ settings.margin_left }}" min="0" max="50">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-end mt-3 mb-5">
                    <button type="submit" class="btn btn-primary">{% trans "حفظ الإعدادات" %}</button>
                </div>
            </form>
        </div>

        <div class="col-lg-4">
            <!-- معاينة الباركود -->
            <div class="card settings-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "معاينة الباركود" %}</h5>
                </div>
                <div class="card-body">
                    <div class="preview-container">
                        <div id="barcode-preview-container">
                            <p>{% trans "سيظهر هنا معاينة للباركود بناءً على الإعدادات المحددة" %}</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <button type="button" id="generate-preview" class="btn btn-info btn-sm">{% trans "توليد معاينة" %}</button>
                    </div>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="card settings-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "روابط سريعة" %}</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="{% url 'inventory:barcode_types' %}" class="list-group-item list-group-item-action">
                            <i class="mdi mdi-tag-multiple me-1"></i> {% trans "إدارة أنواع الباركود" %}
                        </a>
                        <a href="{% url 'inventory:scan_barcode' %}" class="list-group-item list-group-item-action">
                            <i class="mdi mdi-barcode-scan me-1"></i> {% trans "مسح الباركود" %}
                        </a>
                        <a href="{% url 'inventory:barcode_logs' %}" class="list-group-item list-group-item-action">
                            <i class="mdi mdi-history me-1"></i> {% trans "سجلات الباركود" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // توليد معاينة الباركود
        $("#generate-preview").click(function() {
            const barcodeType = $("#default_barcode_type").val();
            if (!barcodeType) {
                toastr.warning("{% trans 'يرجى اختيار نوع الباركود أولاً' %}");
                return;
            }

            // إظهار مؤشر التحميل
            $("#barcode-preview-container").html('<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');

            // إرسال طلب لتوليد باركود للمعاينة
            // سنستخدم نوع الباركود المحدد من القائمة المنسدلة كـ type
            // وسنستخدم بيانات افتراضية كـ data
            const selectedBarcodeTypeOption = $("#default_barcode_type option:selected");
            const barcodeTypeCode = selectedBarcodeTypeOption.text().split('(')[1].split(')')[0].trim(); // استخلاص رمز النوع مثل Code128
            const barcodeData = '12345PREVIEW'; // بيانات افتراضية للمعاينة

            $.ajax({
                url: `{% url 'inventory:barcode:generate_barcode' %}?type=${barcodeTypeCode}&data=${barcodeData}`,
                type: "GET", // تغيير نوع الطلب إلى GET
                // لا نحتاج إلى إرسال بيانات إضافية مع GET
                success: function(response, status, xhr) {
                    // التحقق من أن الاستجابة هي صورة
                    if (xhr.getResponseHeader('content-type').indexOf('image') !== -1) {
                        // تحويل البيانات الثنائية للصورة إلى base64
                        const reader = new FileReader();
                        reader.onloadend = function() {
                            const base64data = reader.result;
                            const previewHtml = `
                                <div class="text-center">
                                    <img src="${base64data}" alt="Barcode Preview" class="barcode-preview">
                                    <div class="mt-2">
                                        <strong>${barcodeData} (${barcodeTypeCode})</strong>
                                    </div>
                                    <div class="mt-1 small">
                                        {% trans "معاينة فقط - قد يختلف الشكل النهائي" %}
                                    </div>
                                </div>
                            `;
                            $("#barcode-preview-container").html(previewHtml);
                        }
                        reader.readAsDataURL(new Blob([response])); // قراءة الاستجابة كـ Blob
                    } else {
                        // إذا لم تكن صورة، قد يكون هناك خطأ نصي
                        toastr.error(response || "{% trans 'حدث خطأ غير متوقع أثناء تحميل المعاينة' %}");
                        $("#barcode-preview-container").html(`<p class="text-danger">${response || "{% trans 'حدث خطأ غير متوقع' %}"}</p>`);
                    }
                },
                error: function(xhr) {
                    let errorMsg = "{% trans 'حدث خطأ أثناء توليد المعاينة' %}";
                    if (xhr.responseText) {
                        errorMsg = xhr.responseText;
                    }
                    toastr.error(errorMsg);
                    $("#barcode-preview-container").html(`<p class="text-danger">${errorMsg}</p>`);
                },
                // ضروري لطلبات GET التي تتوقع بيانات ثنائية (مثل الصور)
                xhrFields: {
                    responseType: 'blob'
                }
            });
        });

        // تحديث معاينة الأبعاد عند تغيير القيم
        $("#label_width, #label_height, #labels_per_row, #labels_per_column").change(function() {
            updateDimensionsPreview();
        });

        function updateDimensionsPreview() {
            const width = $("#label_width").val();
            const height = $("#label_height").val();
            const perRow = $("#labels_per_row").val();
            const perColumn = $("#labels_per_column").val();

            // يمكن إضافة رسم توضيحي هنا لعرض الأبعاد
        }
    });
</script>
{% endblock %}