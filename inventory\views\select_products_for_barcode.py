from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.utils.translation import gettext_lazy as _
from django.contrib import messages

from ..models import Product

@login_required
def select_products_for_barcode(request):
    """
    صفحة وسيطة تسمح للمستخدم باختيار المنتجات قبل طباعة الباركودات
    """
    # الحصول على جميع المنتجات النشطة
    products = Product.objects.filter(is_active=True).order_by('name')
    
    context = {
        'products': products,
        'title': _('اختيار المنتجات لطباعة الباركود'),
    }
    
    return render(request, 'inventory/select_products_for_barcode.html', context)