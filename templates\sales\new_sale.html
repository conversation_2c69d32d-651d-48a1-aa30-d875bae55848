{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "إنشاء بيع جديد" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<style>
    .required-field::after {
        content: " *";
        color: red;
    }

    .form-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .form-section-title {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }

    .product-row {
        transition: all 0.3s;
    }

    .product-row:hover {
        background-color: #f8f9fa;
    }

    .product-image-small {
        width: 50px;
        height: 50px;
        object-fit: contain;
        border-radius: 4px;
        border: 1px solid #ddd;
        padding: 2px;
        background-color: #fff;
        transition: transform 0.2s;
    }

    .product-image-small:hover {
        transform: scale(1.5);
        z-index: 100;
        box-shadow: 0 0 10px rgba(0,0,0,0.2);
    }

    /* تنسيقات ماسح الباركود */
    #scanner-container {
        width: 100%;
        height: 320px;
        overflow: hidden;
        position: relative;
        background-color: #000;
        border-radius: 8px;
    }

    #scanner-video-container {
        width: 100%;
        height: 100%;
        position: relative;
    }

    #scanner-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* حدود منطقة مسح الباركود */
    .barcode-scanner-border {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 70%;
        height: 30%;
        transform: translate(-50%, -50%);
        border: 2px solid #007bff;
        border-radius: 10px;
        box-shadow: 0 0 0 100vw rgba(0, 0, 0, 0.5);
        pointer-events: none;
        z-index: 10;
    }

    /* تأثير عند مسح الباركود */
    .barcode-scanned {
        animation: barcode-success 0.5s;
    }

    @keyframes barcode-success {
        0% { border-color: rgba(40, 167, 69, 0.5); }
        50% { border-color: rgba(40, 167, 69, 1); box-shadow: 0 0 0 100vw rgba(40, 167, 69, 0.2); }
        100% { border-color: rgba(40, 167, 69, 0.5); }
    }

    /* تنسيق الإدخال اليدوي */
    #manual-barcode-input {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    #manual-barcode-btn {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    /* تنسيقات نافذة إضافة المنتج */
    #addProductModal .modal-content {
        border: none;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    #addProductModal .search-container {
        position: relative;
    }

    #addProductModal .input-group {
        border-radius: 10px;
        overflow: hidden;
    }

    #addProductModal .product-image-tiny {
        object-fit: contain;
        background-color: #f8f9fa;
        transition: transform 0.2s;
    }

    #addProductModal .product-item {
        transition: all 0.2s ease;
    }

    #addProductModal .product-item:hover {
        background-color: #f8f9fa;
        transform: translateY(-2px);
    }

    #addProductModal .has-promotion {
        background-color: rgba(25, 135, 84, 0.05);
    }

    #addProductModal .filter-btn {
        border-radius: 20px;
        transition: all 0.2s;
    }

    #addProductModal .filter-btn:hover {
        transform: translateY(-2px);
    }

    #addProductModal .filter-btn.active {
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    #addProductModal .price-container {
        display: flex;
        flex-direction: column;
    }

    #addProductModal .discount-badge {
        margin-top: 3px;
    }

    #addProductModal .product-table-container {
        max-height: 400px;
        overflow-y: auto;
        border-radius: 10px;
        border: 1px solid #eee;
    }

    #addProductModal .select-product {
        transition: all 0.2s;
    }

    #addProductModal .select-product:hover:not([disabled]) {
        transform: scale(1.05);
    }

    .summary-card {
        position: sticky;
        top: 20px;
    }

    .barcode-scanner {
        position: relative;
    }

    .barcode-scanner .scan-icon {
        position: absolute;
        top: 50%;
        left: 10px;
        transform: translateY(-50%);
        cursor: pointer;
    }

    .barcode-scanner .form-control {
        padding-left: 40px;
    }

    #scannerModal .modal-body {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    #scannerModal video {
        width: 100%;
        max-width: 400px;
        border-radius: 10px;
        margin-bottom: 20px;
    }

    /* تنسيقات خانة عرض المدخلات */
    .dataTables_length {
        margin-bottom: 15px;
        margin-right: 15px;
        position: relative;
        display: inline-block;
    }

    .dataTables_length label {
        display: flex;
        align-items: center;
        font-weight: 500;
        font-family: inherit;
        font-size: 0.95rem;
        color: #495057;
        position: relative;
        padding: 0.5rem 0.75rem;
        background-color: #f8f9fa;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        transition: all 0.2s ease;
    }

    .dataTables_length label:hover {
        background-color: #e9ecef;
    }

    .dataTables_length select {
        padding: 0.5rem 2.5rem 0.5rem 1rem;
        margin: 0 0.5rem;
        font-size: 0.95rem;
        font-weight: 500;
        line-height: 1.5;
        color: #3a3a3a;
        background-color: #fff;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%230d6efd' class='bi bi-chevron-down' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 0.75rem center;
        background-size: 16px 12px;
        border: 2px solid #dee2e6;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        transition: all 0.2s ease-in-out;
    }

    .dataTables_length select:focus {
        border-color: #0d6efd;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    .dataTables_length select:hover {
        border-color: #0d6efd;
        cursor: pointer;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(13, 110, 253, 0.15);
    }



    /* تنسيق أيقونة عرض المدخلات */
    .dataTables_length::before {
        content: '\f0b0';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        margin-right: 5px;
        color: #0d6efd;
        font-size: 0.9rem;
    }

    /* Mobile Styles */
    @media (max-width: 767.98px) {
        .dataTables_length {
            width: 100%;
            margin-bottom: 15px;
            text-align: center;
        }

        .dataTables_length label {
            justify-content: center;
            width: 100%;
        }

        .dataTables_length select {
            max-width: 120px;
        }

        .summary-card {
            position: relative;
            top: 0;
            margin-top: 20px;
        }

        .fixed-bottom-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: #fff;
            padding: 10px;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        /* Improve mobile table view */
        #productsTable {
            font-size: 0.9rem;
        }

        #productsTable th:nth-child(1),
        #productsTable td:nth-child(1) {
            width: 40px;
            padding: 0.3rem;
        }

        #productsTable th:nth-child(6),
        #productsTable td:nth-child(6) {
            width: 40px;
            padding: 0.3rem;
        }

        .product-image-small {
            width: 40px;
            height: 40px;
        }

        /* Make action buttons more touch-friendly */
        .btn-sm {
            padding: 0.375rem 0.5rem;
            font-size: 0.9rem;
        }

        /* Add some space at the bottom to prevent content from being hidden behind the fixed bar */
        main {
            padding-bottom: 70px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "إنشاء بيع جديد" %}</h1>
    <a href="{% url 'sales:index' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى المبيعات" %}
    </a>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<form method="post" id="saleForm">
    {% csrf_token %}

    <div class="row">
        <div class="col-md-8">
            <!-- Basic Information Section -->
            <div class="form-section">
                <h5 class="form-section-title">{% trans "المعلومات الأساسية" %}</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="customer" class="form-label required-field">{% trans "العميل" %}</label>
                        <select class="form-select" id="customer" name="customer" required>
                            <option value="">{% trans "اختر العميل" %}</option>
                            {% for customer in customers %}
                            <option value="{{ customer.id }}">{{ customer.name }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">
                            <a href="#" data-bs-toggle="modal" data-bs-target="#quickAddCustomerModal">
                                <i class="fas fa-plus-circle me-1"></i> {% trans "إضافة عميل جديد" %}
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="date" class="form-label required-field">{% trans "تاريخ البيع" %}</label>
                        <input type="date" class="form-control" id="date" name="date" value="{{ today|date:'Y-m-d' }}" required>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="payment_method" class="form-label required-field">{% trans "طريقة الدفع" %}</label>
                        <select class="form-select" id="payment_method" name="payment_method" required>
                            <option value="cash">{% trans "نقدي" %}</option>
                            <option value="card">{% trans "بطاقة ائتمان" %}</option>
                            <option value="transfer">{% trans "تحويل بنكي" %}</option>
                            <option value="check">{% trans "شيك" %}</option>
                            <option value="credit">{% trans "آجل" %}</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="status" class="form-label required-field">{% trans "الحالة" %}</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="completed">{% trans "مكتمل" %}</option>
                            <option value="pending">{% trans "معلق" %}</option>
                        </select>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="notes" class="form-label">{% trans "ملاحظات" %}</label>
                    <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                </div>


            </div>

            <!-- Products Section -->
            <div class="form-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="form-section-title mb-0">{% trans "المنتجات" %}</h5>
                    <div>
                        <button type="button" class="btn btn-sm btn-add-product" data-bs-toggle="modal" data-bs-target="#addProductModal">
                            <i class="fas fa-plus me-1"></i> {% trans "إضافة منتج" %}
                        </button>
                        <button type="button" class="btn btn-sm btn-info ms-2" data-bs-toggle="modal" data-bs-target="#scannerModal">
                            <i class="fas fa-barcode me-1"></i> {% trans "مسح الباركود" %}
                        </button>
                        <button type="button" class="btn btn-sm btn-success ms-2" id="showPromotionsBtn" data-bs-toggle="modal" data-bs-target="#promotionsModal">
                            <i class="fas fa-tag me-1"></i> {% trans "العروض" %}
                        </button>
                        <button type="button" class="btn btn-sm btn-danger ms-2" id="resetCartBtn">
                            <i class="fas fa-trash me-1"></i> {% trans "تفريغ السلة" %}
                        </button>
                    </div>
                </div>

                <div class="barcode-scanner mb-3">
                    <input type="text" class="form-control" id="barcodeInput" placeholder="{% trans 'أدخل الباركود أو اضغط على أيقونة المسح...' %}">
                    <span class="scan-icon" data-bs-toggle="modal" data-bs-target="#scannerModal">
                        <i class="fas fa-barcode"></i>
                    </span>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered" id="productsTable">
                        <thead>
                            <tr>
                                <th width="50px">{% trans "صورة" %}</th>
                                <th>{% trans "المنتج" %}</th>
                                <th width="100px">{% trans "الكمية" %}</th>
                                <th width="150px">{% trans "سعر الوحدة" %}</th>
                                <th width="150px">{% trans "المجموع" %}</th>
                                <th width="50px">{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody id="productsTableBody">
                            <tr id="noProductsRow">
                                <td colspan="6" class="text-center">{% trans "لم يتم إضافة منتجات بعد" %}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-1"></i>
                    {% trans "يمكنك إضافة المنتجات باستخدام زر 'إضافة منتج' أعلاه أو عن طريق مسح الباركود. تأكد من إضافة منتج واحد على الأقل قبل حفظ الفاتورة." %}
                </div>
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3 d-md-none">
                    <button type="submit" class="btn btn-primary" id="mobileSaveBtn">
                        <i class="fas fa-save me-1"></i> {% trans "حفظ الفاتورة" %}
                    </button>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Summary Section -->
            <div class="card shadow summary-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "ملخص الفاتورة" %}</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "المجموع الفرعي:" %}</span>
                            <span id="subtotalSummary">0.00</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "ضريبة القيمة المضافة" %} ({{ tax.rate }}%):</span>
                            <span id="taxSummary">0.00</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="discount" class="form-label">{% trans "الخصم:" %}</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="discount" name="discount" step="0.01" min="0" value="0">
                            <span class="input-group-text">د.م</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="discount_percentage" class="form-label">{% trans "الخصم بالنسبة المئوية:" %}</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="discount_percentage" step="0.1" min="0" max="100" value="0">
                            <span class="input-group-text">%</span>
                            <button class="btn btn-outline-secondary" type="button" id="apply_percentage">{% trans "تطبيق" %}</button>
                        </div>
                        <small class="form-text text-muted">{% trans "أدخل نسبة مئوية واضغط على تطبيق لحساب قيمة الخصم" %}</small>
                    </div>
                    <hr>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <strong>{% trans "المجموع الكلي:" %}</strong>
                            <strong id="totalSummary">0.00</strong>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="amount_paid" class="form-label">{% trans "المبلغ المدفوع:" %}</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="amount_paid" name="amount_paid" step="0.01" min="0" value="0">
                            <span class="input-group-text">د.م</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "المبلغ المتبقي:" %}</span>
                            <span id="remainingAmount">0.00</span>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" id="saveBtn">
                            <i class="fas fa-save me-1"></i> {% trans "حفظ الفاتورة" %}
                        </button>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success" id="saveAndPrintBtn">
                                <i class="fas fa-print me-1"></i> {% trans "حفظ وطباعة" %}
                            </button>
                            <button type="button" class="btn btn-success dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                <span class="visually-hidden">Toggle Dropdown</span>
                            </button>
                            <ul class="dropdown-menu w-100">
                                <li><button type="button" class="dropdown-item" id="saveAndEmailBtn"><i class="fas fa-envelope me-1"></i> {% trans "حفظ وإرسال بالبريد" %}</button></li>
                                <li><button type="button" class="dropdown-item" id="saveAndDownloadBtn"><i class="fas fa-download me-1"></i> {% trans "حفظ وتنزيل PDF" %}</button></li>
                            </ul>
                        </div>
                        <button type="button" class="btn btn-danger" id="confirmSaleBtn">
                            <i class="fas fa-check-circle me-1"></i> {% trans "تأكيد البيع" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addProductModalLabel"><i class="fas fa-box-open me-2"></i>{% trans "إضافة منتج" %}</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <!-- بحث متقدم -->
                <div class="search-container mb-4">
                    <div class="input-group input-group-lg shadow-sm">
                        <span class="input-group-text bg-light border-0">
                            <i class="fas fa-search text-primary"></i>
                        </span>
                        <input type="text" class="form-control form-control-lg border-0 shadow-none" id="productSearch"
                               placeholder="{% trans 'اكتب اسم المنتج أو الكود أو الوصف...' %}">
                        <button class="btn btn-outline-primary" type="button" id="advancedSearchBtn">
                            <i class="fas fa-sliders-h"></i>
                        </button>
                    </div>
                    <div class="form-text text-muted text-center mt-2">
                        <i class="fas fa-info-circle me-1"></i> {% trans "يمكنك البحث بالاسم أو الكود أو الوصف" %}
                    </div>
                </div>

                <!-- فلتر سريع -->
                <div class="quick-filters mb-4 d-flex justify-content-center flex-wrap gap-2">
                    <button class="btn btn-sm btn-outline-secondary filter-btn" data-filter="all">{% trans "الكل" %}</button>
                    <button class="btn btn-sm btn-outline-success filter-btn" data-filter="available">{% trans "متوفر" %}</button>
                    <button class="btn btn-sm btn-outline-danger filter-btn" data-filter="promotion">{% trans "عروض" %}</button>
                    <button class="btn btn-sm btn-outline-info filter-btn" data-filter="popular">{% trans "الأكثر مبيعاً" %}</button>
                </div>

                <!-- جدول المنتجات -->
                <div class="table-responsive product-table-container">
                    <table class="table table-hover align-middle" id="productsSearchTable">
                        <thead class="table-light">
                            <tr>
                                <th>{% trans "الكود" %}</th>
                                <th>{% trans "اسم المنتج" %}</th>
                                <th>{% trans "الفئة" %}</th>
                                <th>{% trans "الكمية المتوفرة" %}</th>
                                <th>{% trans "سعر البيع" %}</th>
                                <th class="d-none">{% trans "الوصف" %}</th>
                                <th>{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr class="product-item {% if product.has_active_promotion %}has-promotion{% endif %} {% if product.quantity > 0 %}in-stock{% endif %}"
                                data-product-id="{{ product.id }}">
                                <td class="fw-bold text-primary">{{ product.code }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if product.image %}
                                        <div class="product-thumbnail me-2">
                                            <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-image-tiny rounded" width="40" height="40">
                                        </div>
                                        {% else %}
                                        <div class="product-thumbnail me-2 bg-light rounded d-flex align-items-center justify-content-center" style="width:40px;height:40px">
                                            <i class="fas fa-box text-secondary"></i>
                                        </div>
                                        {% endif %}
                                        <div>
                                            <div class="product-name fw-medium">{{ product.name }}</div>
                                            {% if product.has_active_promotion %}
                                            <span class="badge bg-danger">{% trans "عرض" %}</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td><span class="badge bg-light text-dark">{{ product.category.name }}</span></td>
                                <td>
                                    {% if product.quantity <= 0 %}
                                    <span class="badge bg-danger">{% trans "غير متوفر" %}</span>
                                    {% elif product.quantity < 5 %}
                                    <span class="badge bg-warning text-dark">{{ product.quantity }}</span>
                                    {% else %}
                                    <span class="badge bg-success">{{ product.quantity }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if product.has_active_promotion %}
                                    <div class="price-container">
                                        <div class="old-price text-decoration-line-through text-muted small">{{ product.selling_price }} د.م</div>
                                        <div class="new-price text-danger fw-bold">{{ product.promotion_price }} د.م</div>
                                        <div class="discount-badge">
                                            <span class="badge bg-success">- {{ product.discount_percentage|floatformat:0 }}%</span>
                                        </div>
                                    </div>
                                    {% else %}
                                    <span class="fw-bold">{{ product.selling_price }} د.م</span>
                                    {% endif %}
                                </td>
                                <td class="d-none">{{ product.description }}</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary select-product rounded-pill"
                                            data-id="{{ product.id }}"
                                            data-code="{{ product.code }}"
                                            data-name="{{ product.name }}"
                                            data-price="{% if product.has_active_promotion %}{{ product.promotion_price }}{% else %}{{ product.selling_price }}{% endif %}"
                                            data-stock="{{ product.quantity }}"
                                            data-image="{% if product.image %}{{ product.image.url }}{% endif %}"
                                            data-has-promotion="{{ product.has_active_promotion|lower }}"
                                            data-regular-price="{{ product.selling_price }}"
                                            data-promotion-price="{% if product.has_active_promotion %}{{ product.promotion_price }}{% endif %}"
                                            {% if product.quantity <= 0 %}disabled="disabled" title="{% trans 'المنتج غير متوفر في المخزون' %}"{% endif %}>
                                        <i class="fas fa-plus me-1"></i> {% trans "إضافة" %}
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- رسالة عدم وجود نتائج -->
                <div id="noProductsFound" class="alert alert-info text-center d-none mt-3">
                    <i class="fas fa-info-circle me-2"></i> {% trans "لم يتم العثور على منتجات مطابقة للبحث" %}
                </div>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> {% trans "إغلاق" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Barcode Scanner Modal -->
<div class="modal fade" id="scannerModal" tabindex="-1" aria-labelledby="scannerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="scannerModalLabel"><i class="fas fa-barcode me-2"></i>{% trans "مسح الباركود" %}</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="scanner-container">
                    <div class="text-center p-3">
                        <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                        <p>{% trans "جاري تهيئة الكاميرا..." %}</p>
                    </div>
                </div>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>{% trans "تعليمات:" %}</strong>
                    <ul class="mb-0 mt-2">
                        <li>{% trans "قم بتوجيه الكاميرا نحو الباركود للمسح التلقائي." %}</li>
                        <li>{% trans "تأكد من وجود إضاءة كافية حول الباركود." %}</li>
                        <li>{% trans "حافظ على ثبات الكاميرا أثناء المسح." %}</li>
                        <li>{% trans "في حالة فشل المسح، يمكنك إدخال الباركود يدوياً." %}</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><i class="fas fa-times me-1"></i>{% trans "إغلاق" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Promotions Modal -->
<div class="modal fade" id="promotionsModal" tabindex="-1" aria-labelledby="promotionsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="promotionsModalLabel">{% trans "العروض الخاصة النشطة" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="promotionsLoading" class="text-center py-4">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">{% trans "جاري التحميل..." %}</span>
                    </div>
                    <p class="mt-2">{% trans "جاري تحميل العروض..." %}</p>
                </div>

                <div id="noPromotions" class="alert alert-info d-none">
                    <i class="fas fa-info-circle me-2"></i> {% trans "لا توجد عروض خاصة نشطة حالياً." %}
                </div>

                <div id="promotionsContainer" class="row g-3 d-none">
                    <!-- سيتم ملء هذا القسم بواسطة JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Email Invoice Modal -->
<div class="modal fade" id="emailInvoiceModal" tabindex="-1" aria-labelledby="emailInvoiceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailInvoiceModalLabel">{% trans "إرسال الفاتورة بالبريد الإلكتروني" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="emailInvoiceForm">
                    <div class="mb-3">
                        <label for="emailTo" class="form-label required-field">{% trans "البريد الإلكتروني للمستلم" %}</label>
                        <input type="email" class="form-control" id="emailTo" required>
                    </div>
                    <div class="mb-3">
                        <label for="emailSubject" class="form-label">{% trans "الموضوع" %}</label>
                        <input type="text" class="form-control" id="emailSubject" value="{% trans "فاتورة من" %} {{ company_info.name|default:"نظام إدارة متجر قطع غيار السيارات" }}">
                    </div>
                    <div class="mb-3">
                        <label for="emailMessage" class="form-label">{% trans "الرسالة" %}</label>
                        <textarea class="form-control" id="emailMessage" rows="4">{% trans "مرحباً,

نرفق لكم فاتورة المشتريات الخاصة بكم. نشكركم على تعاملكم معنا.

مع خالص التقدير,
" %}{{ company_info.name|default:"نظام إدارة متجر قطع غيار السيارات" }}</textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="sendEmailBtn">{% trans "إرسال" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Quick Add Customer Modal -->
<div class="modal fade" id="quickAddCustomerModal" tabindex="-1" aria-labelledby="quickAddCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickAddCustomerModalLabel">{% trans "إضافة عميل جديد" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="quickAddCustomerForm">
                    <div class="mb-3">
                        <label for="customerName" class="form-label required-field">{% trans "اسم العميل" %}</label>
                        <input type="text" class="form-control" id="customerName" required>
                    </div>
                    <div class="mb-3">
                        <label for="customerPhone" class="form-label">{% trans "رقم الهاتف" %}</label>
                        <input type="text" class="form-control" id="customerPhone">
                    </div>
                    <div class="mb-3">
                        <label for="customerEmail" class="form-label">{% trans "البريد الإلكتروني" %}</label>
                        <input type="email" class="form-control" id="customerEmail">
                    </div>
                    <div class="mb-3">
                        <label for="customerAddress" class="form-label">{% trans "العنوان" %}</label>
                        <textarea class="form-control" id="customerAddress" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="saveCustomerBtn">{% trans "حفظ العميل" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<!-- Quagga JS for barcode scanning -->
<script src="https://cdn.jsdelivr.net/npm/quagga@0.12.1/dist/quagga.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTable for products search
        var searchTable = $('#productsSearchTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
            },
            "pageLength": 5
        });

        // Product search
        $('#productSearch').keyup(function() {
            searchTable.search($(this).val()).draw();
        });

        // Select Product
        $(document).on('click', '.select-product', function() {
            var productId = $(this).data('id');
            var productCode = $(this).data('code');
            var productName = $(this).data('name');
            var productPrice = $(this).data('price');
            var productStock = $(this).data('stock');
            var productImage = $(this).data('image');

            // التحقق من توفر المنتج في المخزون
            if (productStock <= 0) {
                alert('{% trans "المنتج غير متوفر في المخزون" %}');
                return;
            }

            // Check if product already exists in the table
            var existingRow = $('input[name="product_ids[]"][value="' + productId + '"]').closest('tr');

            if (existingRow.length > 0) {
                // Product already exists, increment quantity
                var quantityInput = existingRow.find('.quantity-input');
                var currentQuantity = parseInt(quantityInput.val());

                // التحقق من أن الكمية الجديدة لا تتجاوز المخزون
                if (currentQuantity + 1 > productStock) {
                    alert('{% trans "الكمية المطلوبة تتجاوز المخزون المتاح!" %}');
                    return;
                }

                quantityInput.val(currentQuantity + 1).trigger('input');

                // Show notification
                alert('{% trans "تم زيادة كمية المنتج" %}');
            } else {
                // Remove "no products" row if exists
                $('#noProductsRow').remove();

                // Add product to table
                var hasPromotion = $(this).data('has-promotion') === true;
                var regularPrice = $(this).data('regular-price');
                var promotionPrice = $(this).data('promotion-price');
                var promotionClass = hasPromotion ? 'table-success' : '';
                var promotionBadge = hasPromotion ? `<span class="badge bg-danger ms-2">{% trans "عرض" %}</span>` : '';
                var priceDisplay = hasPromotion ?
                    `<div class="small text-muted"><del>${regularPrice}</del> <span class="text-danger fw-bold">${productPrice}</span> د.م</div>` :
                    '';

                var newRow = `
                    <tr class="product-row ${promotionClass}">
                        <td>
                            ${productImage ?
                                `<img src="${productImage}" alt="${productName}" class="product-image-small">` :
                                `<div class="text-center"><i class="fas fa-box text-muted"></i></div>`
                            }
                        </td>
                        <td>
                            <input type="hidden" name="product_ids[]" value="${productId}">
                            <strong>${productCode}</strong> - ${productName} ${promotionBadge}
                            <div class="small text-muted">{% trans "المخزون المتاح:" %} ${productStock}</div>
                            ${priceDisplay}
                        </td>
                        <td>
                            <input type="number" class="form-control form-control-sm quantity-input" name="quantities[]" value="1" min="1" max="${productStock}" required data-stock="${productStock}">
                        </td>
                        <td>
                            <div class="input-group input-group-sm">
                                <input type="number" class="form-control form-control-sm unit-price-input" name="unit_prices[]" value="${productPrice}" step="0.01" min="0" required>
                                <span class="input-group-text">د.م</span>
                            </div>
                        </td>
                        <td>
                            <span class="subtotal">${productPrice}</span> د.م
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-danger remove-product">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;

                $('#productsTableBody').append(newRow);
            }

            // Close modal
            $('#addProductModal').modal('hide');

            // Update summary
            updateSummary();
        });

        // Remove Product
        $(document).on('click', '.remove-product', function() {
            $(this).closest('tr').remove();

            // If no products left, add "no products" row
            if ($('#productsTableBody tr').length === 0) {
                $('#productsTableBody').html('<tr id="noProductsRow"><td colspan="6" class="text-center">{% trans "لم يتم إضافة منتجات بعد" %}</td></tr>');
            }

            // Update summary
            updateSummary();
        });

        // Update subtotal when quantity or unit price changes
        $(document).on('input', '.quantity-input, .unit-price-input', function() {
            updateRowSubtotal($(this).closest('tr'));
        });

        // Quantity increase button
        $(document).on('click', '.quantity-increase', function() {
            var row = $(this).closest('tr');
            var quantityInput = row.find('.quantity-input');
            var currentQuantity = parseInt(quantityInput.val()) || 0;
            var stock = parseInt(quantityInput.data('stock'));

            if (currentQuantity < stock) {
                quantityInput.val(currentQuantity + 1);
                updateRowSubtotal(row);
            } else {
                alert('{% trans "الكمية المطلوبة تتجاوز المخزون المتاح!" %}');
            }
        });

        // Quantity decrease button
        $(document).on('click', '.quantity-decrease', function() {
            var row = $(this).closest('tr');
            var quantityInput = row.find('.quantity-input');
            var currentQuantity = parseInt(quantityInput.val()) || 0;

            if (currentQuantity > 1) {
                quantityInput.val(currentQuantity - 1);
                updateRowSubtotal(row);
            }
        });

        // Function to update row subtotal
        function updateRowSubtotal(row) {
            var quantity = parseFloat(row.find('.quantity-input').val()) || 0;
            var unitPrice = parseFloat(row.find('.unit-price-input').val()) || 0;
            var stock = parseInt(row.find('.quantity-input').data('stock'));
            var productId = row.find('input[name="product_ids[]"]').val();

            // Check if quantity exceeds stock
            if (quantity > stock) {
                alert('{% trans "الكمية المطلوبة تتجاوز المخزون المتاح!" %}');
                row.find('.quantity-input').val(stock);
                quantity = stock;
            }

            var subtotal = quantity * unitPrice;

            row.find('.subtotal').text(subtotal.toFixed(2));

            // Update summary
            updateSummary();

            // تحقق من المخزون باستخدام AJAX
            if (quantity > 1) {
                checkStockAvailability(productId, quantity, row);
            }
        }

        // وظيفة للتحقق من توفر المخزون باستخدام AJAX
        function checkStockAvailability(productId, quantity, row) {
            $.ajax({
                url: '{% url "sales:check_stock" %}',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    product_id: productId,
                    quantity: quantity
                }),
                success: function(response) {
                    if (!response.success) {
                        // الكمية غير متوفرة
                        alert(response.error);
                        row.find('.quantity-input').val(response.available || 1);
                        row.find('.quantity-input').trigger('input');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error checking stock:', error);
                }
            });
        }

        // Update discount
        $('#discount').on('input', function() {
            updateSummary();
        });

        // Apply percentage discount
        $('#apply_percentage').on('click', function() {
            var percentage = parseFloat($('#discount_percentage').val()) || 0;
            if (percentage < 0 || percentage > 100) {
                alert('{% trans "الرجاء إدخال نسبة مئوية صحيحة بين 0 و0 100" %}');
                return;
            }

            var subtotal = 0;
            $('.subtotal').each(function() {
                subtotal += parseFloat($(this).text()) || 0;
            });

            var discountAmount = subtotal * (percentage / 100);
            $('#discount').val(discountAmount.toFixed(2)).trigger('input');
        });

        // Update amount paid
        $('#amount_paid').on('input', function() {
            updateSummary();
        });

        // Update summary
        function updateSummary() {
            var subtotal = 0;

            // Calculate subtotal
            $('.subtotal').each(function() {
                subtotal += parseFloat($(this).text()) || 0;
            });

            // Calculate tax
            var taxRate = parseFloat({{ tax.rate }}); // VAT rate from settings
            var tax = subtotal * (taxRate / 100);

            // Get discount
            var discount = parseFloat($('#discount').val()) || 0;

            // Calculate total
            var total = subtotal + tax - discount;
            if (total < 0) total = 0;

            // Get amount paid
            var amountPaid = parseFloat($('#amount_paid').val()) || 0;

            // Calculate remaining amount
            var remaining = total - amountPaid;

            // Update summary
            $('#subtotalSummary').text(subtotal.toFixed(2));
            $('#taxSummary').text(tax.toFixed(2));
            $('#totalSummary').text(total.toFixed(2));
            $('#remainingAmount').text(remaining.toFixed(2));

            // Update amount paid if payment method is cash and total changed
            if ($('#payment_method').val() === 'cash') {
                $('#amount_paid').val(total.toFixed(2));
                $('#remainingAmount').text('0.00');
            }
        }

        // Change payment method
        $('#payment_method').change(function() {
            var method = $(this).val();
            var total = parseFloat($('#totalSummary').text()) || 0;

            if (method === 'cash') {
                $('#amount_paid').val(total.toFixed(2));
                $('#remainingAmount').text('0.00');
            } else if (method === 'credit') {
                $('#amount_paid').val('0.00');
                $('#remainingAmount').text(total.toFixed(2));
            }
        });

        // Barcode input handling
        $('#barcodeInput').keypress(function(e) {
            if (e.which === 13) { // Enter key
                e.preventDefault();
                var barcode = $(this).val().trim();
                if (barcode) {
                    findProductByBarcode(barcode);
                    $(this).val('');
                }
            }
        });

        // Find product by barcode
        function findProductByBarcode(barcode) {
            $.ajax({
                url: "{% url 'inventory:barcode:scan_barcode' %}", // تأكد من أن هذا هو الاسم الصحيح للـ URL الخاص بمسح الباركود
                type: 'POST',
                data: {
                    'barcode': barcode,
                    'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                success: function(data) {
                    if (data.product) {
                        // تم العثور على المنتج
                        console.log('Product found:', data.product);
                        // هنا يجب إضافة المنتج إلى جدول الفاتورة أو قائمة المنتجات
                        // مثال: addProductToCart(data.product);
                        // نظرًا لعدم وجود دالة واضحة لإضافة المنتج، سنقوم بتسجيله في الكونسول مؤقتًا
                        alert('{% trans "تم العثور على المنتج:" %} ' + data.product.name);
                        $('#barcodeInput').val(''); // مسح حقل الإدخال
                    } else if (data.error) {
                        // لم يتم العثور على المنتج أو حدث خطأ آخر
                        alert(data.error);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', status, error);
                    alert('{% trans "حدث خطأ أثناء البحث عن المنتج. يرجى المحاولة مرة أخرى." %}');
                }
            });
        }

        // Initialize barcode scanner
        var scanner = null;
        var initTimeout = null;

        $('#scannerModal').on('shown.bs.modal', function() {
            // إضافة رسالة تحميل
            $('#scanner-container').html(`
                <div class="text-center p-3">
                    <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                    <p>{% trans "جاري تهيئة الكاميرا..." %}</p>
                </div>
                <div id="scanner-video-container" class="d-none">
                    <video id="scanner-video"></video>
                    <div class="barcode-scanner-border"></div>
                </div>
                <div id="manual-entry" class="mt-3 d-none">
                    <div class="input-group">
                        <input type="text" class="form-control" id="manual-barcode-input" placeholder="{% trans "أدخل الباركود يدوياً" %}">
                        <button class="btn btn-primary" type="button" id="manual-barcode-btn">{% trans "إدخال" %}</button>
                    </div>
                </div>
            `);

            // التحقق من وجود مكتبة Quagga
            if (typeof Quagga === 'undefined') {
                showScannerError('{% trans "لم يتم تحميل مكتبة ماسح الباركود بشكل صحيح" %}');
                return;
            }

            // إضافة مؤقت للتوقف بعد 10 ثوانٍ إذا لم تتم تهيئة الكاميرا
            initTimeout = setTimeout(function() {
                if (Quagga) {
                    try {
                        Quagga.stop();
                    } catch (e) {
                        console.log("خطأ عند محاولة إيقاف Quagga:", e);
                    }
                }
                showScannerError('{% trans "انتهت مهلة تهيئة الكاميرا. يرجى المحاولة مرة أخرى أو استخدام الإدخال اليدوي." %}', true);
            }, 10000); // 10 ثوانٍ كحد أقصى للانتظار

            // تهيئة Quagga
            try {
                Quagga.init({
                    inputStream: {
                        name: "Live",
                        type: "LiveStream",
                        target: document.querySelector('#scanner-video'),
                        constraints: {
                            width: 480,
                            height: 320,
                            facingMode: "environment"
                        },
                    },
                    locator: {
                        patchSize: "medium",
                        halfSample: true
                    },
                    numOfWorkers: 1, // تقليل عدد العمال لتحسين الأداء
                    frequency: 10,
                    decoder: {
                        readers: [
                            "code_128_reader",
                            "ean_reader",
                            "ean_8_reader",
                            "code_39_reader",
                            "upc_reader",
                            "upc_e_reader"
                        ], // تقليل عدد القراء لتحسين الأداء
                    },
                }, function(err) {
                    // إلغاء المؤقت بعد الانتهاء من المحاولة
                    clearTimeout(initTimeout);

                    if (err) {
                        console.error("خطأ في تهيئة ماسح الباركود:", err);

                        // تحديد نوع الخطأ وعرض رسالة مناسبة
                        let errorMessage = '{% trans "حدث خطأ أثناء تهيئة ماسح الباركود" %}';

                        if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
                            errorMessage = '{% trans "لم يتم السماح بالوصول إلى الكاميرا. يرجى السماح بالوصول للكاميرا في إعدادات المتصفح." %}';
                        } else if (err.name === 'NotFoundError' || err.name === 'DevicesNotFoundError') {
                            errorMessage = '{% trans "لم يتم العثور على كاميرا متصلة بجهازك." %}';
                        } else if (err.name === 'NotReadableError' || err.name === 'TrackStartError') {
                            errorMessage = '{% trans "لا يمكن الوصول إلى الكاميرا. قد تكون مستخدمة من قبل تطبيق آخر." %}';
                        } else if (err.name === 'OverconstrainedError' || err.name === 'ConstraintNotSatisfiedError') {
                            errorMessage = '{% trans "لا يمكن استخدام الكاميرا بالإعدادات المطلوبة." %}';
                        }

                        // عرض رسالة الخطأ وتفعيل الإدخال اليدوي
                        showScannerError(errorMessage, true);
                        return;
                    }

                    try {
                        Quagga.start();
                        // إظهار عنصر الفيديو بعد التهيئة الناجحة
                        $('#scanner-video-container').removeClass('d-none');
                    } catch (error) {
                        console.error("خطأ في بدء ماسح الباركود:", error);
                        showScannerError('{% trans "حدث خطأ أثناء تشغيل ماسح الباركود" %}', true);
                    }
                });
            } catch (e) {
                clearTimeout(initTimeout);
                console.error("خطأ غير متوقع:", e);
                showScannerError('{% trans "حدث خطأ غير متوقع أثناء تهيئة الماسح" %}', true);
            }

            // عند اكتشاف باركود
            Quagga.onDetected(function(result) {
                var code = result.codeResult.code;

                // إضافة تأثير مرئي عند نجاح المسح
                $('.barcode-scanner-border').addClass('barcode-scanned');

                // عرض رسالة نجاح
                $('#scanner-container').append(`
                    <div class="alert alert-success position-absolute bottom-0 start-0 end-0 m-2">
                        <i class="fas fa-check-circle me-2"></i>
                        {% trans "تم مسح الباركود بنجاح:" %} ${code}
                    </div>
                `);

                // تأخير قليل لإظهار رسالة النجاح قبل إغلاق النافذة
                setTimeout(function() {
                    $('#barcodeInput').val(code);
                    findProductByBarcode(code);
                    $('#scannerModal').modal('hide');
                }, 800);
            });

            // معالجة الإدخال اليدوي للباركود
            $(document).on('click', '#manual-barcode-btn', function() {
                var barcode = $('#manual-barcode-input').val().trim();
                if (barcode) {
                    $('#barcodeInput').val(barcode);
                    findProductByBarcode(barcode);
                    $('#scannerModal').modal('hide');
                }
            });
        });

        // دالة لعرض رسائل الخطأ في نافذة الماسح
        function showScannerError(message, showManualEntry = false) {
            var errorHtml = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>${message}</div>`;
            $('#scanner-container').html(errorHtml);

            // إضافة زر إعادة المحاولة
            $('#scanner-container').append(`
                <div class="d-grid gap-2 mt-3">
                    <button class="btn btn-outline-primary" type="button" id="retry-camera-btn">
                        <i class="fas fa-sync-alt me-2"></i>{% trans "إعادة تشغيل الكاميرا" %}
                    </button>
                </div>
            `);

            // إظهار خيار الإدخال اليدوي إذا كان مطلوباً
            if (showManualEntry) {
                $('#scanner-container').append(`
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        {% trans "يمكنك إدخال الباركود يدوياً بدلاً من ذلك:" %}
                    </div>
                    <div class="input-group mt-3">
                        <input type="text" class="form-control" id="manual-barcode-input" placeholder="{% trans "أدخل الباركود يدوياً" %}">
                        <button class="btn btn-primary" type="button" id="manual-barcode-btn">{% trans "إدخال" %}</button>
                    </div>
                `);
            }
        }

        $('#scannerModal').on('hidden.bs.modal', function() {
            // إيقاف الكاميرا عند إغلاق النافذة
            if (Quagga) {
                try {
                    Quagga.stop();
                } catch (e) {
                    console.log("خطأ عند إيقاف الكاميرا:", e);
                }
            }

            // إلغاء أي مؤقتات معلقة
            if (initTimeout) {
                clearTimeout(initTimeout);
                initTimeout = null;
            }
        });

        // إضافة زر لإعادة تشغيل الكاميرا
        $(document).on('click', '#retry-camera-btn', function() {
            $('#scannerModal').modal('hide');
            setTimeout(function() {
                $('#scannerModal').modal('show');
            }, 500);
        });

        // Quick add customer
        $('#saveCustomerBtn').click(function() {
            var name = $('#customerName').val();
            var phone = $('#customerPhone').val();
            var email = $('#customerEmail').val();
            var address = $('#customerAddress').val();

            if (!name) {
                alert('{% trans "يرجى إدخال اسم العميل" %}');
                return;
            }

            // Here you would normally make an AJAX call to your backend
            // For demo purposes, we'll just add a new option to the select
            var newOption = new Option(name, 'new_' + Date.now());
            $('#customer').append(newOption).val(newOption.value).trigger('change');

            $('#quickAddCustomerModal').modal('hide');

            // Reset form
            $('#quickAddCustomerForm')[0].reset();
        });

        // Form validation
        $('#saleForm').submit(function(e) {
            if ($('#productsTableBody tr').not('#noProductsRow').length === 0) {
                e.preventDefault();
                alert('{% trans "يرجى إضافة منتج واحد على الأقل" %}');
                return false;
            }



            var total = parseFloat($('#totalSummary').text()) || 0;
            var amountPaid = parseFloat($('#amount_paid').val()) || 0;
            var paymentMethod = $('#payment_method').val();

            if (paymentMethod !== 'credit' && amountPaid < total) {
                if (!confirm('{% trans "المبلغ المدفوع أقل من المبلغ الإجمالي. هل تريد المتابعة؟" %}')) {
                    e.preventDefault();
                    return false;
                }
            }

            return true;
        });

        // Save and print button
        $('#saveAndPrintBtn').click(function() {
            // Add a hidden field to indicate print after save
            $('<input>').attr({
                type: 'hidden',
                name: 'print_after_save',
                value: 'true'
            }).appendTo('#saleForm');

            // Submit the form
            $('#saleForm').submit();
        });

        // Save and email button
        $('#saveAndEmailBtn').click(function() {
            // Check if customer has email
            var customerId = $('#customer').val();
            if (!customerId) {
                alert('{% trans "الرجاء اختيار عميل أولاً" %}');
                return;
            }

            // Show email modal
            $('#emailInvoiceModal').modal('show');
        });

        // Send email button
        $('#sendEmailBtn').click(function() {
            // Validate email form
            var emailTo = $('#emailTo').val();
            if (!emailTo) {
                alert('{% trans "الرجاء إدخال البريد الإلكتروني للمستلم" %}');
                return;
            }

            // Add hidden fields for email information
            $('<input>').attr({
                type: 'hidden',
                name: 'email_after_save',
                value: 'true'
            }).appendTo('#saleForm');

            $('<input>').attr({
                type: 'hidden',
                name: 'email_to',
                value: emailTo
            }).appendTo('#saleForm');

            $('<input>').attr({
                type: 'hidden',
                name: 'email_subject',
                value: $('#emailSubject').val()
            }).appendTo('#saleForm');

            $('<input>').attr({
                type: 'hidden',
                name: 'email_message',
                value: $('#emailMessage').val()
            }).appendTo('#saleForm');

            // Close modal
            $('#emailInvoiceModal').modal('hide');

            // Submit the form
            $('#saleForm').submit();
        });

        // Save and download PDF button
        $('#saveAndDownloadBtn').click(function() {
            // Add a hidden field to indicate download after save
            $('<input>').attr({
                type: 'hidden',
                name: 'download_after_save',
                value: 'true'
            }).appendTo('#saleForm');

            // Submit the form
            $('#saleForm').submit();
        });

        // Confirm sale button
        $('#confirmSaleBtn').click(function() {
            // التحقق من وجود منتجات في السلة
            if ($('#productsTableBody tr').not('#noProductsRow').length === 0) {
                alert('{% trans "يرجى إضافة منتج واحد على الأقل" %}');
                return false;
            }

            // التأكيد قبل المتابعة
            if (confirm('{% trans "هل أنت متأكد من تأكيد البيع؟ سيتم تحديث المخزون تلقائياً." %}')) {
                // إضافة حقل مخفي لتأكيد البيع
                $('<input>').attr({
                    type: 'hidden',
                    name: 'confirm_sale',
                    value: 'true'
                }).appendTo('#saleForm');

                // إرسال النموذج
                $('#saleForm').submit();
            }
        });

        // Mobile save button (same as regular save)
        $('#mobileSaveBtn').click(function() {
            // Submit the form
            $('#saleForm').submit();
        });

        // Reset cart button (تفريغ السلة)
        $('#resetCartBtn').click(function() {
            if (confirm('{% trans "هل أنت متأكد من رغبتك في تفريغ السلة بالكامل؟ سيتم حذف جميع المنتجات المضافة." %}')) {
                // Remove all products
                $('#productsTableBody').html('<tr id="noProductsRow"><td colspan="6" class="text-center">{% trans "لم يتم إضافة منتجات بعد" %}</td></tr>');

                // Reset summary
                updateSummary();

                // Show success message
                alert('{% trans "تم تفريغ السلة بنجاح" %}');
            }
        });

        // تحميل العروض الترويجية
        $('#showPromotionsBtn').click(function() {
            loadPromotions();
        });

        function loadPromotions() {
            // إظهار مؤشر التحميل
            $('#promotionsLoading').removeClass('d-none');
            $('#noPromotions').addClass('d-none');
            $('#promotionsContainer').addClass('d-none').empty();

            // تحميل العروض باستخدام AJAX
            $.ajax({
                url: '{% url "sales:get_promotions" %}',
                type: 'GET',
                success: function(response) {
                    $('#promotionsLoading').addClass('d-none');

                    if (!response.success || response.promotions_count === 0) {
                        $('#noPromotions').removeClass('d-none');
                        return;
                    }

                    // عرض المنتجات التي لديها عروض
                    var container = $('#promotionsContainer').removeClass('d-none');

                    $.each(response.products, function(index, product) {
                        var discountBadge = `<span class="badge bg-danger">${product.discount_percentage}% {% trans "خصم" %}</span>`;
                        var priceDisplay = `<del class="text-muted">${product.regular_price}</del> <span class="text-danger fw-bold">${product.promotion_price}</span> د.م`;
                        var stockClass = product.quantity <= 0 ? 'text-danger' : 'text-success';
                        var stockText = product.quantity <= 0 ? '{% trans "غير متوفر" %}' : `${product.quantity} {% trans "متوفر" %}`;
                        var disabled = product.quantity <= 0 ? 'disabled' : '';

                        var card = `
                            <div class="col-md-4">
                                <div class="card h-100 border-success">
                                    ${product.image_url ?
                                        `<img src="${product.image_url}" class="card-img-top p-2" alt="${product.name}" style="height: 150px; object-fit: contain;">` :
                                        `<div class="text-center p-4"><i class="fas fa-box fa-3x text-muted"></i></div>`
                                    }
                                    <div class="card-body">
                                        <h6 class="card-title">${product.name} ${discountBadge}</h6>
                                        <p class="card-text small">
                                            <strong>{% trans "الكود:" %}</strong> ${product.code}<br>
                                            <strong>{% trans "السعر:" %}</strong> ${priceDisplay}<br>
                                            <strong>{% trans "المخزون:" %}</strong> <span class="${stockClass}">${stockText}</span>
                                        </p>
                                    </div>
                                    <div class="card-footer bg-transparent border-success">
                                        <button type="button" class="btn btn-sm btn-success w-100 add-promotion-to-cart"
                                                data-id="${product.id}"
                                                data-code="${product.code}"
                                                data-name="${product.name}"
                                                data-price="${product.promotion_price}"
                                                data-stock="${product.quantity}"
                                                data-has-promotion="true"
                                                data-regular-price="${product.regular_price}"
                                                data-promotion-price="${product.promotion_price}"
                                                ${disabled}>
                                            <i class="fas fa-cart-plus me-1"></i> {% trans "إضافة للسلة" %}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;

                        container.append(card);
                    });

                    // إضافة معالج النقر لأزرار إضافة العروض
                    $('.add-promotion-to-cart').click(function() {
                        var productId = $(this).data('id');
                        var productCode = $(this).data('code');
                        var productName = $(this).data('name');
                        var productPrice = $(this).data('price');
                        var productStock = $(this).data('stock');
                        var hasPromotion = $(this).data('has-promotion');
                        var regularPrice = $(this).data('regular-price');
                        var promotionPrice = $(this).data('promotion-price');

                        // التحقق من توفر المنتج في المخزون
                        if (productStock <= 0) {
                            alert('{% trans "المنتج غير متوفر في المخزون" %}');
                            return;
                        }

                        // التحقق من وجود المنتج بالفعل في السلة
                        var existingRow = $('input[name="product_ids[]"][value="' + productId + '"]').closest('tr');

                        if (existingRow.length > 0) {
                            // المنتج موجود بالفعل، زيادة الكمية
                            var quantityInput = existingRow.find('.quantity-input');
                            var currentQuantity = parseInt(quantityInput.val());

                            if (currentQuantity + 1 > productStock) {
                                alert('{% trans "الكمية المطلوبة تتجاوز المخزون المتاح!" %}');
                                return;
                            }

                            quantityInput.val(currentQuantity + 1).trigger('input');
                            alert('{% trans "تم زيادة كمية المنتج" %}');
                        } else {
                            // إزالة صف "لا توجد منتجات" إذا كان موجوداً
                            $('#noProductsRow').remove();

                            // إضافة المنتج إلى السلة
                            var promotionClass = hasPromotion ? 'table-success' : '';
                            var promotionBadge = hasPromotion ? `<span class="badge bg-danger ms-2">{% trans "عرض" %}</span>` : '';
                            var priceDisplay = hasPromotion ?
                                `<div class="small text-muted"><del>${regularPrice}</del> <span class="text-danger fw-bold">${productPrice}</span> د.م</div>` :
                                '';

                            var newRow = `
                                <tr class="product-row ${promotionClass}">
                                    <td>
                                        <div class="text-center"><i class="fas fa-box text-muted"></i></div>
                                    </td>
                                    <td>
                                        <input type="hidden" name="product_ids[]" value="${productId}">
                                        <strong>${productCode}</strong> - ${productName} ${promotionBadge}
                                        <div class="small text-muted">{% trans "المخزون المتاح:" %} ${productStock}</div>
                                        ${priceDisplay}
                                    </td>
                                    <td>
                                        <div class="input-group input-group-sm">
                                            <button type="button" class="btn btn-outline-secondary quantity-decrease"><i class="fas fa-minus"></i></button>
                                            <input type="number" class="form-control form-control-sm quantity-input" name="quantities[]" value="1" min="1" max="${productStock}" required data-stock="${productStock}">
                                            <button type="button" class="btn btn-outline-secondary quantity-increase"><i class="fas fa-plus"></i></button>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control form-control-sm unit-price-input" name="unit_prices[]" value="${productPrice}" step="0.01" min="0" required>
                                            <span class="input-group-text">د.م</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="subtotal">${productPrice}</span> د.م
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-danger remove-product">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            `;

                            $('#productsTableBody').append(newRow);
                        }

                        // تحديث الملخص
                        updateSummary();

                        // إغلاق النافذة المنبثقة
                        $('#promotionsModal').modal('hide');
                    });
                },
                error: function(xhr, status, error) {
                    $('#promotionsLoading').addClass('d-none');
                    $('#noPromotions').removeClass('d-none')
                        .html(`<i class="fas fa-exclamation-circle me-2"></i> {% trans "حدث خطأ أثناء تحميل العروض:" %} ${error}`);
                    console.error('Error loading promotions:', error);
                }
            });
        }

        // Add fixed bottom bar for mobile view
        if (window.innerWidth < 768) {
            $('<div class="fixed-bottom-bar d-md-none">' +
              '<div class="container">' +
              '<div class="d-flex justify-content-between align-items-center">' +
              '<div><strong>{% trans "المجموع:" %}</strong> <span id="mobileTotal">0.00</span> {% trans "د.م" %}</div>' +
              '<button type="submit" class="btn btn-primary"><i class="fas fa-save me-1"></i> {% trans "حفظ" %}</button>' +
              '</div></div></div>').appendTo('body');

            // Update mobile total when summary changes
            function updateMobileTotal() {
                $('#mobileTotal').text($('#totalSummary').text());
            }

            // Call initially and when summary updates
            updateMobileTotal();
            $('#totalSummary').on('DOMSubtreeModified', updateMobileTotal);
        }

        // Initialize summary
        updateSummary();
    });

    // تضمين ملف تحسينات نافذة إضافة المنتج
    $.getScript('/static/js/product-modal.js');

</script>
{% endblock %}
