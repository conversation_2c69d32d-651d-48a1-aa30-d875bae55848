<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الباركود</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .barcode-test {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 5px;
        }
        .barcode-test h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .barcode-test img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ccc;
            padding: 10px;
            background: white;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار أنواع الباركود المختلفة</h1>
        
        <div class="barcode-test">
            <h3>Code 128 - الأكثر شيوعاً</h3>
            <img src="http://127.0.0.1:8000/inventory/barcode/generate/?type=code128&data=123456789" 
                 alt="Code 128 Barcode" 
                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <div class="error" style="display:none;">فشل في تحميل باركود Code 128</div>
        </div>

        <div class="barcode-test">
            <h3>EAN-13 - للمنتجات التجارية</h3>
            <img src="http://127.0.0.1:8000/inventory/barcode/generate/?type=ean13&data=123456789012" 
                 alt="EAN-13 Barcode" 
                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <div class="error" style="display:none;">فشل في تحميل باركود EAN-13</div>
        </div>

        <div class="barcode-test">
            <h3>EAN-8 - للمنتجات الصغيرة</h3>
            <img src="http://127.0.0.1:8000/inventory/barcode/generate/?type=ean8&data=1234567" 
                 alt="EAN-8 Barcode" 
                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <div class="error" style="display:none;">فشل في تحميل باركود EAN-8</div>
        </div>

        <div class="barcode-test">
            <h3>Code 39</h3>
            <img src="http://127.0.0.1:8000/inventory/barcode/generate/?type=code39&data=123456789" 
                 alt="Code 39 Barcode" 
                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <div class="error" style="display:none;">فشل في تحميل باركود Code 39</div>
        </div>

        <div class="barcode-test">
            <h3>UPC-A</h3>
            <img src="http://127.0.0.1:8000/inventory/barcode/generate/?type=upca&data=12345678901" 
                 alt="UPC-A Barcode" 
                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <div class="error" style="display:none;">فشل في تحميل باركود UPC-A</div>
        </div>

        <div style="margin-top: 30px;">
            <p><strong>ملاحظة:</strong> إذا ظهرت جميع الباركودات بشكل صحيح، فهذا يعني أن النظام يعمل بشكل مثالي!</p>
            <p><a href="http://127.0.0.1:8000/inventory/barcode/generate/" target="_blank" style="color: #007bff; text-decoration: none;">← العودة إلى صفحة إنشاء الباركود</a></p>
        </div>
    </div>

    <script>
        // إضافة رسالة نجاح عند تحميل الصور بنجاح
        document.querySelectorAll('img').forEach(function(img) {
            img.onload = function() {
                var successDiv = document.createElement('div');
                successDiv.className = 'success';
                successDiv.textContent = '✓ تم تحميل الباركود بنجاح';
                this.parentNode.appendChild(successDiv);
            };
        });
    </script>
</body>
</html>
